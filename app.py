from flask import Flask, render_template, jsonify, send_from_directory,request, send_file
import sqlite3
import os
import base64
from io import BytesIO
from werkzeug.utils import secure_filename
import shutil
import platform
import sys
from PyQt5.QtWidgets import QApplication, QMainWindow
from PyQt5.QtWebEngineWidgets import QWebEngineView, QWebEnginePage
from PyQt5.QtCore import QUrl
import threading
from PyQt5.QtGui import QIcon
##############################################################################################################
app = Flask(__name__)
# Chemin vers la base de données
DB_PATH = 'db/mydb.db'  # Ajustez le chemin selon votre structure de projet
# Fonction pour obtenir une connexion à la base de données
def get_db_connection():
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row  # Pour accéder aux résultats par nom de colonne
    return conn
############# html ###########################################################################################
@app.route('/')
def index():
    return render_template('index.html')
############# category db ####################################################################################
@app.route('/api/niches_by_category/<category_name>')
def get_niches_by_category(category_name):
    conn = get_db_connection()
    # D'abord, obtenez l'ID de la catégorie
    category = conn.execute('SELECT id FROM Categories WHERE name = ?', (category_name,)).fetchone()
    if not category:
        conn.close()
        return jsonify([])
    # Ensuite, récupérez toutes les niches pour cette catégorie
    niches = conn.execute('''
        SELECT * FROM Niches
        WHERE category_id = ?
        ORDER BY name
    ''', (category['id'],)).fetchall()
    conn.close()
    # Convertir en liste de dictionnaires
    niches_list = [dict(niche) for niche in niches]
    return jsonify(niches_list)
############ designs niches ####################################################################################
@app.route('/api/designs_by_niche/<int:niche_id>')
def get_designs_by_niche(niche_id):
    conn = get_db_connection()

    # Récupérer tous les designs/images pour cette niche
    designs = conn.execute('''
        SELECT * FROM Images
        WHERE niche_id = ?
        ORDER BY created_at DESC
    ''', (niche_id,)).fetchall()

    conn.close()

    # Convertir les objets Row en dictionnaires
    designs_list = []
    for design in designs:
        design_dict = dict(design)
        designs_list.append(design_dict)

    return jsonify(designs_list)
#####################categorys #########################################################################################
@app.route('/api/categories')
def get_categories():
    conn = get_db_connection()

    # Récupérer toutes les catégories
    categories = conn.execute('SELECT id, name FROM Categories ORDER BY name').fetchall()
    conn.close()

    # Convertir en liste de dictionnaires
    categories_list = [dict(category) for category in categories]
    return jsonify(categories_list)
############ add niche ####################################################################################
@app.route('/api/niches', methods=['POST'])
def create_niche():
    # Récupérer les données JSON envoyées
    data = request.json

    # Vérifier que les données nécessaires sont présentes
    if not data or 'name' not in data or 'category_id' not in data:
        return jsonify({'error': 'Données manquantes'}), 400

    try:
        # 1. Ajouter à la base de données
        conn = get_db_connection()

        # Récupérer le nom de la catégorie
        category = conn.execute('SELECT name FROM Categories WHERE id = ?',
                               (data['category_id'],)).fetchone()

        if not category:
            conn.close()
            return jsonify({'error': 'Catégorie non trouvée'}), 404

        category_name = category['name']
        niche_name = data['name']

        # Insérer la nouvelle niche
        cursor = conn.execute('''
            INSERT INTO Niches (name, category_id)
            VALUES (?, ?)
        ''', (niche_name, data['category_id']))

        niche_id = cursor.lastrowid
        conn.commit()
        conn.close()

        # 2. Créer les dossiers nécessaires - CHEMIN CORRIGÉ
        base_path = os.getcwd()

        # Nouveau chemin: static/niches/[category_name]/[niche_name]
        niches_dir = os.path.join(base_path, "static", "niches")
        category_dir = os.path.join(niches_dir, category_name)
        niche_dir = os.path.join(category_dir, niche_name)

        print(f"Tentative de création du dossier: {niche_dir}")

        # Création forcée des répertoires parents
        try:
            if not os.path.exists(category_dir):
                os.makedirs(category_dir, exist_ok=True)
                print(f"Dossier catégorie créé: {category_dir}")

            if not os.path.exists(niche_dir):
                os.makedirs(niche_dir, exist_ok=True)
                print(f"Dossier niche créé: {niche_dir}")
        except Exception as folder_error:
            print(f"Erreur lors de la création des dossiers: {folder_error}")
            return jsonify({'error': f'Erreur lors de la création du dossier: {str(folder_error)}'}), 500

        return jsonify({
            'id': niche_id,
            'name': niche_name,
            'category_id': data['category_id'],
            'category_name': category_name,
            'folder_path': niche_dir,
            'message': 'Niche ajoutée avec succès et dossier créé'
        }), 201

    except sqlite3.Error as e:
        print(f"Erreur SQLite: {str(e)}")
        return jsonify({'error': f'Erreur de base de données: {str(e)}'}), 500
    except Exception as e:
        print(f"Erreur inattendue: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'Erreur: {str(e)}'}), 500
############ update niche name ####################################################################################
############ modif niche name ###########################################################################################################
@app.route('/api/update_niche_name', methods=['POST'])
def update_niche_name():
    # Récupérer les données JSON envoyées
    data = request.json

    # Vérifier que les données nécessaires sont présentes
    if not data or 'id' not in data or 'new_name' not in data or 'category_name' not in data:
        return jsonify({'error': 'Données manquantes'}), 400

    niche_id = data['id']
    new_name = data['new_name']
    category_name = data['category_name']

    try:
        conn = get_db_connection()

        # 1. Récupérer l'ancien nom de la niche
        old_niche = conn.execute('SELECT name FROM Niches WHERE id = ?', (niche_id,)).fetchone()

        if not old_niche:
            conn.close()
            return jsonify({'error': 'Niche non trouvée'}), 404

        old_name = old_niche['name']
        print(f"Modification de niche: {old_name} -> {new_name} dans la catégorie {category_name}")

        # 2. Mettre à jour le nom dans la table Niches
        conn.execute('UPDATE Niches SET name = ? WHERE id = ?', (new_name, niche_id))
        print(f"Nom mis à jour dans la table Niches")

        # 3. Mettre à jour TOUS les chemins d'images associées à cette niche
        # Récupérer TOUTES les images liées à cette niche par son ID
        images = conn.execute('SELECT id, file_path FROM Images WHERE niche_id = ?', (niche_id,)).fetchall()

        print(f"Trouvé {len(images) if images else 0} images associées à cette niche")

        # Mettre à jour chaque chemin d'image en RECONSTRUISANT le chemin complet
        updated_count = 0
        for image in images:
            try:
                old_path = image['file_path']

                # Extraire uniquement le nom du fichier (dernière partie du chemin)
                file_name = os.path.basename(old_path)

                # Construire le nouveau chemin au format standard
                new_path = f"static/niches/{category_name}/{new_name}/{file_name}"

                print(f"Mise à jour du chemin: {old_path} -> {new_path}")
                conn.execute('UPDATE Images SET file_path = ? WHERE id = ?', (new_path, image['id']))
                updated_count += 1
            except Exception as e:
                print(f"Erreur lors de la mise à jour du chemin pour image {image['id']}: {str(e)}")

        print(f"Total de {updated_count} chemins d'images mis à jour")

        # Assurez-vous de faire un commit pour sauvegarder les modifications
        conn.commit()
        conn.close()

        # 4. Gérer les dossiers physiques
        try:
            base_path = os.getcwd()
            niches_dir = os.path.join(base_path, "static", "niches")
            category_dir = os.path.join(niches_dir, category_name)

            if os.path.exists(category_dir):
                # Lister tous les dossiers dans cette catégorie
                for entry in os.listdir(category_dir):
                    # Vérifier si ce dossier correspond à la niche (avec ou sans suffixe)
                    if entry == old_name or entry.startswith(f"{old_name}-") or entry == f"Niche-{niche_id}":
                        old_dir_path = os.path.join(category_dir, entry)
                        new_dir_path = os.path.join(category_dir, new_name)

                        # Vérifier si le dossier de destination existe déjà
                        if os.path.exists(new_dir_path):
                            print(f"Le dossier de destination existe déjà: {new_dir_path}")

                            # Copier les fichiers du dossier source vers le dossier de destination
                            for file_name in os.listdir(old_dir_path):
                                source_file = os.path.join(old_dir_path, file_name)
                                dest_file = os.path.join(new_dir_path, file_name)

                                if os.path.isfile(source_file):
                                    import shutil
                                    shutil.copy2(source_file, dest_file)
                                    print(f"Fichier copié: {source_file} -> {dest_file}")

                            # Supprimer l'ancien dossier après la copie
                            import shutil
                            shutil.rmtree(old_dir_path)
                            print(f"Ancien dossier supprimé: {old_dir_path}")
                        else:
                            # Renommer directement le dossier
                            print(f"Renommage du dossier: {old_dir_path} -> {new_dir_path}")
                            os.rename(old_dir_path, new_dir_path)
                            print(f"Dossier renommé avec succès")
            else:
                print(f"Répertoire de catégorie non trouvé: {category_dir}")
                # Créer les répertoires nécessaires
                new_niche_dir = os.path.join(category_dir, new_name)
                os.makedirs(new_niche_dir, exist_ok=True)
                print(f"Nouveaux répertoires créés: {new_niche_dir}")

        except Exception as dir_error:
            print(f"Erreur lors de la gestion des dossiers: {str(dir_error)}")
            import traceback
            traceback.print_exc()
            # Continue même si la gestion des dossiers échoue

        return jsonify({
            'success': True,
            'id': niche_id,
            'old_name': old_name,
            'new_name': new_name,
            'message': 'Nom de la niche modifié avec succès'
        })

    except sqlite3.Error as e:
        print(f"Erreur SQLite: {str(e)}")
        return jsonify({'error': f'Erreur de base de données: {str(e)}'}), 500
    except Exception as e:
        print(f"Erreur inattendue: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'Erreur: {str(e)}'}), 500
#######################################################################################################################
############ delete niche ####################################################################################
@app.route('/api/delete_niche', methods=['POST'])
def delete_niche():
    # Récupérer les données JSON envoyées
    data = request.json

    # Vérifier que les données nécessaires sont présentes
    if not data or 'id' not in data or 'niche_name' not in data or 'category_name' not in data:
        return jsonify({'error': 'Données manquantes'}), 400

    niche_id = data['id']
    niche_name = data['niche_name']
    category_name = data['category_name']

    try:
        conn = get_db_connection()

        # 1. Vérifier que la niche existe
        niche = conn.execute('SELECT id FROM Niches WHERE id = ?', (niche_id,)).fetchone()

        if not niche:
            conn.close()
            return jsonify({'error': 'Niche non trouvée'}), 404

        # 2. Supprimer les références dans la table Images (si elles existent)
        conn.execute('DELETE FROM Images WHERE niche_id = ?', (niche_id,))

        # 3. Supprimer la niche de la base de données
        conn.execute('DELETE FROM Niches WHERE id = ?', (niche_id,))

        conn.commit()
        conn.close()

        # 4. Supprimer le dossier correspondant
        base_path = os.getcwd()
        niches_dir = os.path.join(base_path, "static", "niches")
        category_dir = os.path.join(niches_dir, category_name)
        niche_dir = os.path.join(category_dir, niche_name)

        if os.path.exists(niche_dir):
            import shutil
            shutil.rmtree(niche_dir)  # Supprime récursivement le dossier et son contenu
            print(f"Dossier supprimé: {niche_dir}")
        else:
            print(f"Dossier non trouvé: {niche_dir}")

        return jsonify({
            'success': True,
            'id': niche_id,
            'message': 'Niche supprimée avec succès'
        })

    except sqlite3.Error as e:
        print(f"Erreur SQLite: {str(e)}")
        return jsonify({'error': f'Erreur de base de données: {str(e)}'}), 500
    except Exception as e:
        print(f"Erreur inattendue: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'Erreur: {str(e)}'}), 500
 ######################dep niche to archive############################################################################################
 ############ archive niche ####################################################################################
@app.route('/api/archive_niche', methods=['POST'])
def archive_niche():
    # Récupérer les données JSON envoyées
    data = request.json

    # Vérifier que les données nécessaires sont présentes
    if not data or 'id' not in data or 'niche_name' not in data or 'category_name' not in data:
        return jsonify({'error': 'Données manquantes'}), 400

    niche_id = data['id']
    niche_name = data['niche_name']
    category_name = data['category_name']

    try:
        conn = get_db_connection()

        # Vérifier si la niche existe
        niche = conn.execute('SELECT id FROM Niches WHERE id = ?', (niche_id,)).fetchone()

        if not niche:
            conn.close()
            return jsonify({'error': 'Niche non trouvée'}), 404

        # Supprimer d'abord les références dans la table Images (si elles existent)
        conn.execute('DELETE FROM Images WHERE niche_id = ?', (niche_id,))

        # Supprimer la niche de la base de données
        conn.execute('DELETE FROM Niches WHERE id = ?', (niche_id,))

        conn.commit()
        conn.close()

        # Chemin du dossier à déplacer
        base_path = os.getcwd()
        source_dir = os.path.join(base_path, "static", "niches", category_name, niche_name)

        # Vérifier si le dossier source existe
        if not os.path.exists(source_dir):
            print(f"Dossier source non trouvé: {source_dir}")
            return jsonify({
                'success': False,
                'message': f'Niche supprimée dans la DB mais le dossier source non trouvé: {source_dir}'
            }), 404

        # Créer le dossier archive s'il n'existe pas
        archive_dir = os.path.join(base_path, "static", "archive")
        if not os.path.exists(archive_dir):
            os.makedirs(archive_dir, exist_ok=True)

        # Destination dans le dossier archive
        dest_dir = os.path.join(archive_dir, f"{category_name}_{niche_name}")

        # Si un dossier avec le même nom existe déjà dans l'archive, ajoutez un timestamp
        if os.path.exists(dest_dir):
            import time
            timestamp = time.strftime("%Y%m%d-%H%M%S")
            dest_dir = os.path.join(archive_dir, f"{category_name}_{niche_name}_{timestamp}")

        # Déplacer le dossier
        import shutil
        print(f"Déplacement de {source_dir} vers {dest_dir}")
        shutil.move(source_dir, dest_dir)

        return jsonify({
            'success': True,
            'message': 'Niche supprimée et déplacée vers les archives avec succès'
        })

    except sqlite3.Error as e:
        print(f"Erreur SQLite: {str(e)}")
        return jsonify({'error': f'Erreur de base de données: {str(e)}'}), 500
    except Exception as e:
        print(f"Erreur inattendue: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'Erreur: {str(e)}'}), 500
 ############ updates designs titre brnd ####################################################################################
 ############ update design details ####################################################################################
@app.route('/api/update_design_details', methods=['POST'])
def update_design_details():
    # Récupérer les données JSON envoyées
    data = request.json

    # Vérifier que les données nécessaires sont présentes
    if not data or 'id' not in data:
        return jsonify({'error': 'Données manquantes'}), 400

    design_id = data['id']

    try:
        conn = get_db_connection()

        # Vérifier que le design existe
        design = conn.execute('SELECT * FROM Images WHERE id = ?', (design_id,)).fetchone()

        if not design:
            conn.close()
            return jsonify({'error': 'Design non trouvé'}), 404

        # Construire la requête de mise à jour
        update_fields = []
        update_values = []

        # Même si title est une chaîne vide ou null, nous le mettons à jour
        if 'title' in data:
            update_fields.append("title = ?")
            update_values.append(data['title'])

        # Même si brand est une chaîne vide ou null, nous le mettons à jour
        if 'brand' in data:
            update_fields.append("brand = ?")
            update_values.append(data['brand'])

        # Si aucune mise à jour n'est demandée, retourner un message approprié
        if not update_fields:
            conn.close()
            return jsonify({'message': 'Aucune modification demandée'}), 200

        # Ajouter l'ID à la fin des valeurs pour la clause WHERE
        update_values.append(design_id)

        # Exécuter la mise à jour
        sql_query = f"UPDATE Images SET {', '.join(update_fields)} WHERE id = ?"
        conn.execute(sql_query, update_values)

        # Valider les modifications
        conn.commit()

        # Vérifier que les modifications ont été appliquées
        updated_design = conn.execute('SELECT * FROM Images WHERE id = ?', (design_id,)).fetchone()
        print(f"Design après mise à jour: {dict(updated_design)}")  # Convertir en dict pour l'affichage

        conn.close()

        return jsonify({
            'success': True,
            'id': design_id,
            'message': 'Détails du design mis à jour avec succès'
        })

    except sqlite3.Error as e:
        print(f"Erreur SQLite: {str(e)}")
        return jsonify({'error': f'Erreur de base de données: {str(e)}'}), 500
    except Exception as e:
        print(f"Erreur inattendue: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'Erreur: {str(e)}'}), 500
############ delete design ####################################################################################
############ delete design ####################################################################################
@app.route('/api/delete_design', methods=['POST'])
def delete_design():
    # Récupérer les données JSON envoyées
    data = request.json

    # Vérifier que les données nécessaires sont présentes
    if not data or 'id' not in data or 'niche_name' not in data or 'category_name' not in data:
        return jsonify({'error': 'Données manquantes'}), 400

    design_id = data['id']
    niche_name = data['niche_name']
    category_name = data['category_name']

    try:
        conn = get_db_connection()

        # 1. Récupérer les informations du design (notamment le chemin du fichier)
        design = conn.execute('SELECT * FROM Images WHERE id = ?', (design_id,)).fetchone()

        if not design:
            conn.close()
            return jsonify({'error': 'Design non trouvé'}), 404

        file_path = design['file_path']

        # 2. Supprimer le design de la base de données
        conn.execute('DELETE FROM Images WHERE id = ?', (design_id,))

        conn.commit()
        conn.close()

        # 3. Supprimer le fichier physique
        base_path = os.getcwd()
        full_file_path = os.path.join(base_path, file_path)

        if os.path.exists(full_file_path):
            os.remove(full_file_path)
            print(f"Fichier supprimé: {full_file_path}")
        else:
            print(f"Fichier non trouvé: {full_file_path}")

        return jsonify({
            'success': True,
            'id': design_id,
            'message': 'Design supprimé avec succès'
        })

    except sqlite3.Error as e:
        print(f"Erreur SQLite: {str(e)}")
        return jsonify({'error': f'Erreur de base de données: {str(e)}'}), 500
    except Exception as e:
        print(f"Erreur inattendue: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'Erreur: {str(e)}'}), 500
############ archive design ####################################################################################
@app.route('/api/archive_design', methods=['POST'])
def archive_design():
    # Récupérer les données JSON envoyées
    data = request.json

    # Vérifier que les données nécessaires sont présentes
    if not data or 'id' not in data:
        return jsonify({'error': 'Données manquantes'}), 400

    design_id = data['id']

    try:
        conn = get_db_connection()

        # 1. Récupérer les informations du design (notamment le chemin du fichier)
        design = conn.execute('SELECT * FROM Images WHERE id = ?', (design_id,)).fetchone()

        if not design:
            conn.close()
            return jsonify({'error': 'Design non trouvé'}), 404

        file_path = design['file_path']
        file_name = os.path.basename(file_path)

        # 2. Supprimer le design de la base de données
        conn.execute('DELETE FROM Images WHERE id = ?', (design_id,))

        conn.commit()
        conn.close()

        # 3. Déplacer le fichier vers les archives
        base_path = os.getcwd()

        # Chemin source du fichier
        source_file_path = os.path.join(base_path, file_path)

        # Vérifier si le fichier source existe
        if not os.path.exists(source_file_path):
            print(f"Fichier source non trouvé: {source_file_path}")
            return jsonify({
                'success': False,
                'message': f'Design supprimé de la base de données mais fichier non trouvé: {source_file_path}'
            }), 404

        # Créer le dossier archive s'il n'existe pas
        archive_dir = os.path.join(base_path, "static", "archive")
        if not os.path.exists(archive_dir):
            os.makedirs(archive_dir, exist_ok=True)

        # Chemin de destination - directement dans le dossier archive
        dest_file_path = os.path.join(archive_dir, file_name)

        # Si un fichier avec le même nom existe déjà, ajouter un timestamp
        if os.path.exists(dest_file_path):
            import time
            timestamp = time.strftime("%Y%m%d-%H%M%S")
            file_name_parts = os.path.splitext(file_name)
            new_file_name = f"{file_name_parts[0]}_{timestamp}{file_name_parts[1]}"
            dest_file_path = os.path.join(archive_dir, new_file_name)

        # Déplacer le fichier
        import shutil
        print(f"Déplacement de {source_file_path} vers {dest_file_path}")
        shutil.move(source_file_path, dest_file_path)

        return jsonify({
            'success': True,
            'id': design_id,
            'archive_path': dest_file_path,
            'message': 'Design supprimé et déplacé vers les archives avec succès'
        })

    except sqlite3.Error as e:
        print(f"Erreur SQLite: {str(e)}")
        return jsonify({'error': f'Erreur de base de données: {str(e)}'}), 500
    except Exception as e:
        print(f"Erreur inattendue: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'Erreur: {str(e)}'}), 500
############ save new design ####################################################################################
@app.route('/api/upload_design', methods=['POST'])
def upload_design():
    # Vérifier si tous les champs requis sont présents
    if 'file' not in request.files or not request.form.get('niche_id') or \
       not request.form.get('title') or not request.form.get('brand'):
        return jsonify({'error': 'Données manquantes'}), 400

    file = request.files['file']
    niche_id = request.form.get('niche_id')
    title = request.form.get('title')
    brand = request.form.get('brand')

    # Vérifier si le fichier est vide
    if file.filename == '':
        return jsonify({'error': 'Aucun fichier sélectionné'}), 400

    try:
        conn = get_db_connection()

        # Vérifier que la niche existe
        niche = conn.execute('SELECT * FROM Niches WHERE id = ?', (niche_id,)).fetchone()

        if not niche:
            conn.close()
            return jsonify({'error': 'Niche non trouvée'}), 404

        # Récupérer les informations de la niche
        niche_dict = dict(niche)
        niche_name = niche_dict['name']

        # Récupérer la catégorie
        category = conn.execute('SELECT name FROM Categories WHERE id = ?', (niche_dict['category_id'],)).fetchone()
        category_name = category['name']

        # Créer le chemin du dossier
        base_path = os.getcwd()
        niche_dir = os.path.join(base_path, "static", "niches", category_name, niche_name)

        # S'assurer que le dossier existe
        if not os.path.exists(niche_dir):
            os.makedirs(niche_dir, exist_ok=True)
            print(f"Dossier niche créé: {niche_dir}")

        # Sécuriser le nom du fichier
        import re

        # Obtenir l'extension du fichier original
        filename, file_extension = os.path.splitext(file.filename)

        # Créer un nom de fichier basé sur le titre (sans espaces ni caractères spéciaux)
        safe_title = re.sub(r'[^a-zA-Z0-9]', '_', title)

        # Ajouter un timestamp pour éviter les doublons
        import time
        timestamp = time.strftime("%Y%m%d-%H%M%S")

        # Nom final du fichier
        final_filename = f"{safe_title}_{timestamp}{file_extension}"

        # Chemin complet du fichier
        file_path = os.path.join(niche_dir, final_filename)
        relative_path = os.path.join("static", "niches", category_name, niche_name, final_filename)

        # Enregistrer le fichier
        file.save(file_path)

        # Ajouter l'entrée dans la base de données
        from datetime import datetime
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        cursor = conn.execute('''
            INSERT INTO Images (niche_id, file_path, title, brand, status, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (niche_id, relative_path, title, brand, "not uploaded", now))

        design_id = cursor.lastrowid

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'id': design_id,
            'file_path': relative_path,
            'message': 'Design téléchargé avec succès'
        })

    except sqlite3.Error as e:
        print(f"Erreur SQLite: {str(e)}")
        return jsonify({'error': f'Erreur de base de données: {str(e)}'}), 500
    except Exception as e:
        print(f"Erreur inattendue: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'Erreur: {str(e)}'}), 500
############# statu design ####################################################################################
@app.route('/api/update_design_status', methods=['POST'])
def update_design_status():
    # Récupérer les données JSON envoyées
    data = request.json

    # Vérifier que les données nécessaires sont présentes
    if not data or 'id' not in data or 'status' not in data:
        return jsonify({'error': 'Données manquantes'}), 400

    design_id = data['id']
    status = data['status']
    niche_name = data.get('niche_name', '')
    category_name = data.get('category_name', '')

    # Convertir le statut en minuscules pour correspondre à la contrainte de la base de données
    status_lower = status.lower()

    # Valider que le statut est l'une des valeurs attendues (en minuscules)
    if status_lower not in ["uploaded", "not uploaded"]:
        return jsonify({'error': 'Statut non valide'}), 400

    try:
        conn = get_db_connection()

        # Vérifier que le design existe
        design = conn.execute('SELECT * FROM Images WHERE id = ?', (design_id,)).fetchone()

        if not design:
            conn.close()
            return jsonify({'error': 'Design non trouvé'}), 404

        # Mettre à jour le statut (en utilisant la version minuscule)
        conn.execute('UPDATE Images SET status = ? WHERE id = ?', (status_lower, design_id))

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'id': design_id,
            'status': status,  # Renvoyer le statut original pour l'affichage
            'niche_name': niche_name,
            'category_name': category_name,
            'message': 'Statut du design mis à jour avec succès'
        })

    except sqlite3.Error as e:
        print(f"Erreur SQLite: {str(e)}")
        return jsonify({'error': f'Erreur de base de données: {str(e)}'}), 500
    except Exception as e:
        print(f"Erreur inattendue: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'Erreur: {str(e)}'}), 500
############# statu ads ####################################################################################
@app.route('/api/update_design_ads', methods=['POST'])
def update_design_ads():
    # Récupérer les données JSON envoyées
    data = request.json

    # Vérifier que les données nécessaires sont présentes
    if not data or 'id' not in data:
        return jsonify({'error': 'Données manquantes'}), 400

    design_id = data['id']
    ads_value = data.get('ads_value', 'ads')  # Valeur par défaut est "ads"

    try:
        conn = get_db_connection()

        # Vérifier que le design existe
        design = conn.execute('SELECT * FROM Images WHERE id = ?', (design_id,)).fetchone()

        if not design:
            conn.close()
            return jsonify({'error': 'Design non trouvé'}), 404

        # Mettre à jour avec la valeur fournie
        conn.execute("UPDATE Images SET ads = ? WHERE id = ?", (ads_value, design_id))

        # Important: Faire un commit explicite
        conn.commit()

        # Vérifier si la mise à jour a bien été effectuée
        verification = conn.execute('SELECT ads FROM Images WHERE id = ?', (design_id,)).fetchone()
        print(f"Vérification après mise à jour: ads={verification['ads']}")

        conn.close()

        return jsonify({
            'success': True,
            'id': design_id,
            'ads_value': ads_value,
            'message': f'Statut ads du design mis à jour avec succès ({ads_value})'
        })

    except sqlite3.Error as e:
        print(f"Erreur SQLite: {str(e)}")
        return jsonify({'error': f'Erreur de base de données: {str(e)}'}), 500
    except Exception as e:
        print(f"Erreur inattendue: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'Erreur: {str(e)}'}), 500
############# upload to merch####################################################################################
@app.route('/api/save_designs_to_desktop', methods=['POST'])
def save_designs_to_desktop():
    # Récupérer les chemins des images et les informations associées
    data = request.json
    if not data or 'designs' not in data:
        return jsonify({'error': 'Données manquantes'}), 400

    designs = data['designs']

    if not designs:
        return jsonify({'error': 'Aucun design sélectionné'}), 400

    try:
        # Déterminer le chemin du bureau selon le système d'exploitation
        if platform.system() == 'Windows':
            desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop')
        elif platform.system() == 'Darwin':  # macOS
            desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop')
        else:  # Linux et autres
            desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop')
            if not os.path.exists(desktop_path):
                desktop_path = os.path.join(os.path.expanduser('~'), 'Bureau')

        # Créer le dossier "upload to merch" s'il n'existe pas
        target_dir = os.path.join(desktop_path, 'upload to merch')
        if not os.path.exists(target_dir):
            os.makedirs(target_dir)
            print(f"Dossier créé: {target_dir}")
        else:
            # Vider le dossier s'il existe déjà
            for file_name in os.listdir(target_dir):
                file_path = os.path.join(target_dir, file_name)
                try:
                    if os.path.isfile(file_path):
                        os.unlink(file_path)
                        print(f"Fichier supprimé: {file_path}")
                    elif os.path.isdir(file_path):
                        shutil.rmtree(file_path)
                        print(f"Sous-dossier supprimé: {file_path}")
                except Exception as e:
                    print(f"Erreur lors de la suppression de {file_path}: {e}")
            print(f"Dossier vidé: {target_dir}")

        saved_files = []

        for design in designs:
            image_path = design.get('path')
            title = design.get('title', 'unknown_title')
            brand = design.get('brand', 'unknown_brand')
            design_id = design.get('id', '0')

            # Vérifier que le chemin d'image est valide
            if not image_path:
                continue

            # Convertir le chemin relatif en chemin absolu
            # Enlever le préfixe '/' si présent
            if image_path.startswith('/'):
                image_path = image_path[1:]

            full_path = os.path.join(os.getcwd(), image_path)

            # Vérifier si le fichier existe
            if not os.path.exists(full_path):
                print(f"Fichier non trouvé: {full_path}")
                continue

            # Créer un nom de fichier propre
            safe_filename = f"{brand}_{title}_{design_id}.png".replace(' ', '_')

            # Chemin de destination
            dest_path = os.path.join(target_dir, safe_filename)

            # Copier le fichier
            shutil.copy2(full_path, dest_path)
            saved_files.append(safe_filename)
            print(f"Fichier copié: {full_path} -> {dest_path}")

        return jsonify({
            'success': True,
            'message': f'{len(saved_files)} design(s) ont été sauvegardés dans le dossier "upload to merch" sur le bureau (dossier vidé au préalable).',
            'files': saved_files,
            'destination': target_dir
        })

    except Exception as e:
        print(f"Erreur lors de la sauvegarde des fichiers: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'Erreur: {str(e)}'}), 500
###############################################################################################################
def run_flask():
    # Lance le serveur Flask sur un port spécifique
    app.run(debug=False, port=5555)

if __name__ == '__main__':
    # Démarrer Flask dans un thread séparé
    flask_thread = threading.Thread(target=run_flask)
    flask_thread.daemon = True  # Le thread se fermera quand le programme principal se termine
    flask_thread.start()

    # Créer l'application PyQt
    qt_app = QApplication(sys.argv)

    # Appliquer une feuille de style personnalisée à l'application
    style_sheet = """
        QMainWindow {
            background-color: rgb(218, 117, 9);
            color: white;
        }
        QStatusBar {
            background-color: #1a1a1a;
            color: #cccccc;
        }
        QMenuBar {
            background-color: #2d2d2d;
            color: white;
        }
        QMenuBar::item:selected {
            background-color: #3d3d3d;
        }
    """
    qt_app.setStyleSheet(style_sheet)

    # Créer la fenêtre principale
    main_window = QMainWindow()
    main_window.setWindowTitle("MERCHDAYS")
    main_window.resize(1280, 800)  # Taille par défaut

    # Ajouter une barre de statut avec un message
    status_bar = main_window.statusBar()
    status_bar.showMessage("Prêt")
    status_bar.hide()
    # Charger l'icône
    icon_path = os.path.join(os.getcwd(), "static/icons", "logo.png")
    if os.path.exists(icon_path):
        icon = QIcon(icon_path)
        main_window.setWindowIcon(icon)
    else:
        print(f"Icône non trouvée: {icon_path}")

    # Ajouter un composant navigateur web à la fenêtre
    web_view = QWebEngineView()

    # Configurer le gestionnaire de liens externes
    class WebEnginePage(QWebEnginePage):
        def javaScriptConsoleMessage(self, level, message, lineNumber, sourceID):
            # Surcharge de la méthode pour déboguer les messages de la console JavaScript
            print(f"JS Console [{level}] Line {lineNumber}: {message}")

        def acceptNavigationRequest(self, url, _type, isMainFrame):
            url_string = url.toString()
            print(f"Navigation request: {url_string}, type: {_type}, isMainFrame: {isMainFrame}")

            # Vérifier si c'est l'URL d'activation spécifique
            if "mbf1972.github.io/merchdays-site/activation.html" in url_string:
                print(f"Detected activation URL: {url_string}")
                import webbrowser
                webbrowser.open(url_string)
                print(f"Opened in external browser: {url_string}")
                return False  # Ne pas naviguer dans la fenêtre PyQt

            # Si c'est une navigation vers un lien externe (pas localhost)
            if "localhost" not in url_string and (_type == QWebEnginePage.NavigationTypeLinkClicked or _type == QWebEnginePage.NavigationTypeRedirect):
                print(f"External link detected: {url_string}")
                import webbrowser
                webbrowser.open(url_string)
                print(f"Opened in external browser: {url_string}")
                return False  # Ne pas naviguer dans la fenêtre PyQt

            return super().acceptNavigationRequest(url, _type, isMainFrame)

        def createWindow(self, _type):
            # Cette méthode est appelée quand window.open() est utilisé en JavaScript
            print(f"createWindow called with type: {_type}")
            return None  # Retourner None pour bloquer l'ouverture d'une nouvelle fenêtre

    # Appliquer la page personnalisée au navigateur
    web_page = WebEnginePage(web_view)
    web_view.setPage(web_page)

    # Injecter du JavaScript pour intercepter les événements personnalisés
    js_code = """
    document.addEventListener('openExternalLink', function(e) {
        if (e && e.detail && e.detail.url) {
            console.log('openExternalLink event received for URL: ' + e.detail.url);
            // Notifier Python via un objet window spécial
            if (typeof window.pywebview !== 'undefined') {
                window.pywebview.api.openExternalLink(e.detail.url);
            } else {
                // Fallback pour PyQt
                console.log('PyQt: Opening external URL: ' + e.detail.url);
                // Créer un élément avec un ID spécial que Python peut détecter
                var linkInfo = document.createElement('div');
                linkInfo.id = 'pyqt-external-link';
                linkInfo.setAttribute('data-url', e.detail.url);
                linkInfo.style.display = 'none';
                document.body.appendChild(linkInfo);

                // Supprimer l'élément après un court délai
                setTimeout(function() {
                    if (linkInfo.parentNode) {
                        linkInfo.parentNode.removeChild(linkInfo);
                    }
                }, 1000);
            }
        }
    });

    // Fonction globale pour ouvrir des liens externes
    window.openExternalUrl = function(url) {
        console.log('openExternalUrl called with: ' + url);
        var event = new CustomEvent('openExternalLink', {
            detail: { url: url }
        });
        document.dispatchEvent(event);
        return false;
    };

    // Remplacer window.open pour les liens externes
    var originalWindowOpen = window.open;
    window.open = function(url, target, features) {
        console.log('window.open intercepted: ' + url);
        if (url && url.indexOf('http') === 0 && url.indexOf('localhost') === -1) {
            console.log('External URL detected in window.open: ' + url);
            return window.openExternalUrl(url);
        }
        return originalWindowOpen.apply(this, arguments);
    };
    """

    # Fonction pour injecter le JavaScript après le chargement de la page
    def on_load_finished(ok):
        if ok:
            web_view.page().runJavaScript(js_code)
            print("JavaScript injected successfully")
        else:
            print("Page failed to load")

    # Connecter le signal loadFinished
    web_view.loadFinished.connect(on_load_finished)

    # Fonction pour surveiller le DOM et détecter les éléments spéciaux
    def check_for_external_links():
        # Script pour vérifier la présence d'éléments spéciaux
        check_script = """
        (function() {
            var linkElement = document.getElementById('pyqt-external-link');
            if (linkElement) {
                var url = linkElement.getAttribute('data-url');
                if (url) {
                    return url;
                }
            }
            return '';
        })();
        """

        # Fonction de callback pour traiter le résultat
        def handle_check_result(result):
            if result and isinstance(result, str) and result.startswith('http'):
                print(f"External link detected in DOM: {result}")
                import webbrowser
                webbrowser.open(result)
                print(f"Opened in external browser: {result}")

        # Exécuter le script et connecter le callback
        web_view.page().runJavaScript(check_script, handle_check_result)

    # Créer un timer pour vérifier périodiquement les liens externes
    from PyQt5.QtCore import QTimer
    external_link_timer = QTimer()
    external_link_timer.timeout.connect(check_for_external_links)
    external_link_timer.start(500)  # Vérifier toutes les 500 ms

    # Charger l'URL de l'application
    web_view.load(QUrl("http://localhost:5555"))

    # Définir le navigateur comme widget central
    main_window.setCentralWidget(web_view)

    # Afficher la fenêtre et exécuter l'application
    main_window.show()
    sys.exit(qt_app.exec_())

