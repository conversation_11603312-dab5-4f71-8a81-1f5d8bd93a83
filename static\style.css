/*************************************body********************************/
body {
    font-family: 'Segoe UI', '<PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
    background: #10102b;
    margin: 0;
    padding: 0;
    text-align: center;
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 100vh; /* Assure que le body prend au moins toute la hauteur de la fenêtre */
    box-sizing: border-box; /* Inclut les paddings et borders dans la hauteur */
}

/*************************************header****************************************************************/
.header {
    background: linear-gradient(128deg, #0b012b, #da7509);
    border-radius: 0 0 15px 15px;
    padding: 12px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    height: 10%; /* Hauteur fixe au lieu de pourcentage */
    width: 100%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    position: relative;
    z-index: 10;
}
.header img {
    width: 3%;
    height: 100%;
    margin-left: 80px;
    border-radius: 50px;
    transition: transform 0.3s ease;
}
.header img:hover {
    transform: scale(1.1);
}
.title {
    font-size: clamp(28px, 4vw, 45px);
    background: none;
    font-weight: 600;
    color: white;
    font-family:'Lucida Sans', sans-serif;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
    letter-spacing: 0.5px;
}

.title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background-color: white;
    animation: slide-in 2.5s ease-in-out infinite;
}

@keyframes slide-in {
    0% { left: -100%; }
    50% { left: 0; }
    100% { left: 100%; }
}

.dttogle {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
}
.header .logoo {
    display: flex;
    width: 11%;
    height: 90%;
    border-radius: 1px;
}
.logoo {
    display: flex;
    background-color:transparent;
 }
.date-box {
    padding: 8px 15px;
    margin-right: 20px;
    border-radius: 20px;
    background-color: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(5px);
    font-family: 'Segoe UI', sans-serif;
    font-weight: 600;
    font-size: clamp(12px, 1.8vw, 20px);
    color: white;
    white-space: nowrap;
    display: flex;
    align-items: center;
    justify-content: center;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.date-box:hover {
    background-color: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/***************************main container****************************************************************/
.sadr{
    display: flex;
    background-color: #10102b;
    height:80%; /* Hauteur calculée pour laisser de l'espace pour le header et le footer */
    width: 100%;
    gap: 10px;
    padding: 10px 0;
    margin-bottom: 10px; /* Marge inférieure pour créer de l'espace pour le footer */
}

/***************boutons*************************bouton**********************************************/
.category-niches {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 20%;
    border-radius: 12px;
    margin: 0 5px 0 10px;
    /*background-color: #fcfcfc;*/
    box-shadow: 0 3px 10px rgba(0,0,0,0.08);
}
.btncategory {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: auto;
    width: 92%;
    border-radius: 12px;
    margin-bottom: 10px;
    padding: 15px 0;
    background-color: #4a6cf7;
}

.dtup {
    display: flex !important;
    flex-direction: row !important;
    width: 100% !important;
    height: 35px !important;
    min-height: 35px !important;
    max-height: 35px !important;
    margin-bottom: 3px !important;
    flex-shrink: 0 !important; /* Empêche l'élément de se rétrécir */
    flex-grow: 0 !important; /* Empêche l'élément de s'agrandir */
    box-sizing: border-box !important; /* Inclut les paddings et borders dans la hauteur */
}

.uplod {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40%;
    height: 100%;
    font-size: 15px;
    font-weight: 600;
    color: white;
   /* background-color: #e31010;*/
    border-radius: 8px 0 0 0;
}
.uplodx {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40%;
    height: 100%;
    font-size: 15px;
    font-weight: 600;
    color: white;
    background-color: #06a22d;
    border-radius: 8px 0 0 0;
}
.image-container {
    position: relative;
    display: inline-block;
    width: 100%;
    overflow: visible; /* Permet aux éléments enfants de déborder si nécessaire */
}

.hover-controls {
    position: absolute;
    bottom: 5px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
    padding: 5px;
    border-radius: 20px;
    opacity: 0; /* Invisible par défaut */
    transition: opacity 0.3s ease;
    z-index: 10;
    width: 20%;
    margin: 0 auto;
    pointer-events: none; /* Permet de cliquer à travers le conteneur */
}

/* Les éléments à l'intérieur de hover-controls auront des événements de pointeur */
.hover-controls > * {
    pointer-events: auto;
}

/* Style spécial pour les checkboxes cochées qui restent visibles */
.image-container .chqbox:checked {
    opacity: 1 !important;
    visibility: visible !important;
    position: absolute !important;
    top: 5px !important;
    left: 5px !important;
    z-index: 30 !important;
}

.hover-controls .editbtnn,
.hover-controls .mockupbtnn {
    width: 30px;
    height: 30px;
    margin: 0;
    padding: 0;
}

/* Style personnalisé pour la checkbox */
.hover-controls .chqbox {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 3px; /* Carré avec coins arrondis pour un look plus standard */
    background-color: #ffffff;
    border: 2px solid #f5630a;
    cursor: pointer;
    position: absolute; /* Position absolue pour le placer où on veut */
    top: -305px; /* En haut */
    left: -128px; /* À gauche */
    margin: 0;
    padding: 0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
    z-index: 20; /* S'assurer qu'il est au-dessus de l'image */
    opacity: 0; /* Invisible par défaut, apparaît au survol */
}

.hover-controls .chqbox:checked {
    background-color: #f5630a;
    opacity: 1 !important; /* Toujours visible quand cochée */
    /* Conserver exactement la même position */
    top: -305px !important;
    left: -128px !important;
}

.hover-controls .chqbox:checked::after {
    content: "✓";
    position: absolute;
    color: white;
    font-size: 14px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.image-container:hover .hover-controls {
    opacity: 1;
}

/* Style pour les boutons d'édition et de mockup (qui apparaissent au survol) */
.hover-controls .editbtnn,
.hover-controls .mockupbtnn,
.hover-controls .chqbox:not(:checked) {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-container:hover .hover-controls .editbtnn,
.image-container:hover .hover-controls .mockupbtnn,
.image-container:hover .hover-controls .chqbox:not(:checked) {
    opacity: 1;
}

/* Assure que les checkboxes cochées restent visibles même sans survol */
.image-container .hover-controls .chqbox:checked {
    opacity: 1 !important;
    visibility: visible !important;
    z-index: 30 !important;
    /* Conserver exactement la même position */
    top: -305px !important;
    left: -128px !important;
}

/* Style pour les conteneurs qui ont des checkboxes cochées */
.hover-controls[data-has-checked="true"] .chqbox:checked {
    opacity: 1 !important;
    visibility: visible !important;
    /* Conserver exactement la même position */
    top: -305px !important;
    left: -128px !important;
}

/* Assure que la checkbox reste visible même quand le conteneur est invisible */
.hover-controls[data-has-checked="true"] {
    opacity: 1 !important;
    visibility: visible !important;
    background-color: transparent !important;
    /* Ne pas modifier les dimensions ou la position */
    pointer-events: none !important;
}

/* Permet aux éléments à l'intérieur du conteneur de recevoir des événements */
.hover-controls[data-has-checked="true"] > * {
    pointer-events: auto !important;
}

.chqbox {
    display: flex;
    margin: 0;
    justify-content: flex-start;
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.chqsp {
    white-space: nowrap;
    margin-left: 10px;
    color: white;
    font-weight: bold;
}
.dt {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 60%;
    height: 100%;
    font-size: 15px;
    font-weight: 600;
    color: #030753;
    background-color: white;
}
.ads {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 17%;
    height: 100%;
    font-size: 15px;
    font-weight: 600;
    color: hsla(240, 7%, 97%, 1);
    background-color: rgb(243, 6, 6);
    border-radius: 0 8px 0 0;
}
.adsoho {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 17%;
    height: 100%;
    font-size: 15px;
    font-weight: 600;
    color: hsla(240, 7%, 97%, 1);
    background-color: white;
    border-radius: 0 8px 0 0;
}
.niche-item:hover {
    background: #ff5903;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(74, 108, 247, 0.3);
}
.edit-button {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24'%3E%3Cpath fill='white' d='M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z'/%3E%3C/svg%3E");
    background-size: 60%;
    background-repeat: no-repeat;
    background-position: center;
    background-color: #0c0d47;
    border: 2px solid #f5630a;
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    position: absolute;
    right: -48px;
    border-radius: 47px;
    margin-right: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    padding: 3px 8px;
    min-height: 32px;
    min-width: 32px;
}
.edit-button:hover {
    background-color: #32447a;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24'%3E%3Cpath fill='%23f5630a' d='M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z'/%3E%3C/svg%3E");
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}
.xmx {
    display: flex;
    margin: 0;
    margin-left: 0;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    height: 100%;
    width: 100%;
    position: relative;
    overflow: hidden; /* Pour empêcher tout débordement */
}

.listniches {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    border-radius: 0 0 12px 12px;
    background-color: #10102b;
    width: 99%;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 10px 0;
    box-sizing: border-box;
    position: absolute; /* Position absolue dans le parent */
    top: [position-top-appropriée]; /* Ajustez selon votre mise en page */
    top: 89px;
    max-height: 90%;
    max-height: 88.7%; /* Garde la proportion originale */
}
.niche-item {
    position: relative;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    color: white;
    font-size: 18px;
    font-weight: bold;
    margin-top: 5px;
    margin-left: -35px;
    width: 88%;
    min-height: 59px; /* Changé de height à min-height */
    max-height: fit-content; /* Permet l'expansion mais avec contrôle */
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(74, 108, 247, 0.2);
    transition: all 0.2s ease;
    border-radius: 20px;
    background-origin: border-box;
    background-clip: content-box, border-box;
    background-image: linear-gradient(#10102b, #10102b), linear-gradient(to right, #ff5903, #ffb700, #ff5903);
    border: 2px solid transparent;
    flex-shrink: 0; /* Empêche les éléments de se rétrécir */
    margin-bottom: 5px; /* Ajoute un espacement en bas */
}
/* Stylisation de la barre de défilement */
.listniches {
    /* Personnalisation de la barre de défilement */
    scrollbar-width: thin; /* Pour Firefox */
    scrollbar-color: #888888 #000000; /* Pour Firefox */
}

.listniches::-webkit-scrollbar {
    width: 12px; /* Largeur de la barre de défilement verticale */
}

.listniches::-webkit-scrollbar-track {
    background: #000000; /* Fond noir */
    border-radius: 0;
}

.listniches::-webkit-scrollbar-thumb {
    background: #888888; /* Poignée grise */
    border-radius: 6px;
    border: 2px solid #000000; /* Bordure noire autour de la poignée */
    min-height: 80px; /* Hauteur minimale de la poignée */
}

.listniches::-webkit-scrollbar-thumb:hover {
    background: #aaaaaa; /* Gris plus clair au survol */
}

/* Ajout des boutons de défilement (flèches) */
.listniches::-webkit-scrollbar-button {
    display: block;
    height: 18px; /* Augmenté de 12px à 18px */
    width: 18px; /* Augmenté de 12px à 18px */
    background-color: #000000;
    border: 1px solid #333333;
}

/* Masquer les boutons de défilement supplémentaires */
.listniches::-webkit-scrollbar-button:start:increment,
.listniches::-webkit-scrollbar-button:end:decrement {
    display: none;
}

/* Flèche haut */
.listniches::-webkit-scrollbar-button:start:decrement {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12"><path fill="%23888888" d="M0 6L6 0L12 6z"/></svg>');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 12px 12px; /* Taille de l'icône SVG */
}

/* Flèche bas */
.listniches::-webkit-scrollbar-button:end:increment {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12"><path fill="%23888888" d="M0 0L12 0L6 6z"/></svg>');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 12px 12px; /* Taille de l'icône SVG */
}
/***************contenu*************************plato**********************************************/
.plato {
    display: flex;
    flex-direction: column;
    height: 98%; /* Hauteur encore plus réduite pour laisser plus d'espace au footer */
    width: 80%;
    border-radius: 12px;
    overflow: hidden;
    margin-right: 10px;
    position: relative; /* Position relative pour le positionnement des éléments enfants */
}

.lesniches {
    background-color: #10102b;
    display: flex;
    flex-direction: column;
    height: 100%; /* Utilise toute la hauteur disponible */
    width: 100%;
    overflow: hidden; /* Empêche le débordement */
}


.catitr {
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
    align-items: center;
    height: 40px; /* Hauteur fixe au lieu de pourcentage */
    width: 99.5%;
    margin: 0;
    position: relative;
    color: white;
    font-weight: bold;
    font-size: 20px; /* Taille de police légèrement réduite */
    border-radius: 50px;
    background-origin: border-box;
    background-clip: content-box, border-box;
    background-image: linear-gradient(#10102b, #10102b), linear-gradient(to right, #ff5903, #ffb700, #ff5903);
    border: 2px solid transparent; /* Bordure réduite */
}
/* Conteneur pour les boutons de toggle */
.toggle-container {
    display: flex;
    justify-content: center;
    align-items: center;
    /*background-color: #54545b;*/
    border-radius: 50px;
    height: 85%;
    width: 20%;
    padding: 3px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    gap: 10px;
}
/* Style commun pour les boutons de toggle */
.toggle-btn {
    color: white;
    font-size: 13px;
    font-weight: bold;
    cursor: pointer;
    padding: 8px 20px;
    height: 90%;
    width: 45%;
    border: none;
    background-color: transparent;
    z-index: 10;
    transition: all 0.2s ease;
    border-radius: 50px;
}

/* Style pour le bouton actif Uploaded (rouge) */
#upld.active {
    background-color: #e74c3c;
    color: white;
}

/* Style pour le bouton actif Not Uploaded (vert) */
#notup.active {
    background-color: #27ae60;
    color: white;
}

/* Style pour le survol */
.toggle-btn:hover:not(.active) {
    background-color: #2c3e50;
}
.categ {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    top: 6px;
    height: 4.5%;
    width: 96%;
    color: white;
    font-weight: bold;
    font-size: 22px;
    border-radius: 50px;
    background-origin: border-box;
    background-clip: content-box, border-box;
    background-image: linear-gradient(#10102b, #10102b), linear-gradient(to right, #ff5903, #ffb700, #ff5903);
    border: 2px solid transparent;
}

.titro{
    display: flex;
    justify-content: center;
    align-items: center;
    height: 82%;
    width: 60%;
    border-radius: 50px;
    color: white;
    font-weight: bold;
    font-size: 22px;
}

/* Styles pour la bande passante d'images */
.tsawer {
    display: flex;
    align-items: center;
    background-color: #10102b;
    justify-content: center;
    width: 99.5%;
    height: 100%;
    position: relative;
    overflow: hidden; /* Changé de visible à hidden pour éviter les débordements */
    border-radius: 12px;
    margin: 5px;
}

.slider-container {
    width: 99%;
    height: 95%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start; /* Alignement en haut pour éviter les débordements */
    align-items: center;
    position: relative;
    overflow: hidden;
}

.slider-wrapper {
    background-color: #10102b;
    width: 100%;
    height: calc(96% - 40px); /* Hauteur calculée pour laisser de l'espace pour le titre */
    margin-top: 5px;
    overflow: hidden;
    position: relative;
    border-radius: 0 0 10px 10px;
}

.slider {
    display: grid;
    grid-template-columns: repeat(4, minmax(0, 1fr));
    gap: 10px;
    height: 750px;
    width: 99%;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 5px;
    scroll-behavior: smooth; /* Pour un défilement fluide */
    scrollbar-width: thin; /* Pour Firefox */
    scrollbar-color: #888888 #000000; /* Pour Firefox */
    max-height: 100%; /* Assure que le slider ne dépasse pas son conteneur */
    grid-auto-rows: min-content; /* Hauteur des lignes basée sur le contenu */
}
/* Personnalisation de la barre de défilement pour Chrome, Edge et Safari */
.slider::-webkit-scrollbar {
    width: 14px; /* Largeur de la barre de défilement verticale */
    height: auto; /* Hauteur automatique */
}

.slider::-webkit-scrollbar-track {
    background: #000000; /* Fond noir */
    border-radius: 0;
}
.slider::-webkit-scrollbar-thumb {
    background: #888888; /* Poignée grise */
    border-radius: 6px;
    border: 2px solid #000000; /* Bordure noire autour de la poignée */
    min-height: 40px; /* Hauteur minimale de la poignée */
}
.slider::-webkit-scrollbar-thumb:hover {
    background: #aaaaaa; /* Gris plus clair au survol */
}

/* Ajout des boutons de défilement (flèches) */
.slider::-webkit-scrollbar-button {
    display: block;
    height: 12px;
    width: 12px;
    background-color: #000000;
    border: 1px solid #333333;
}

/* Masquer les boutons de défilement supplémentaires */
.slider::-webkit-scrollbar-button:start:increment,
.slider::-webkit-scrollbar-button:end:decrement {
    display: none;
}

/* Flèche gauche */
.slider::-webkit-scrollbar-button:start:decrement {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 8 8"><path fill="%23888888" d="M0 4L4 0L8 4z"/></svg>');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 12px 12px; /* Taille de l'icône SVG */
}

/* Flèche bas */
.slider::-webkit-scrollbar-button:end:increment {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 8 8"><path fill="%23888888" d="M0 4L4 8L8 4z"/></svg>');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 12px 12px; /* Taille de l'icône SVG */
}
/*****************************************************************/
/* Styles des boutons de navigation supprimés pour utiliser le défilement natif */
/***********************************************************************************/
/* Styles des boutons de navigation pour les niches supprimés pour utiliser le défilement natif */
/*********************************************************************************/
/* Styles de la barre de défilement déjà définis plus haut */
/*********************************************************************/
.desimg {
    display: flex;
    flex-direction: column;
    border: 1px #5c5a5a solid;
    height: auto; /* Hauteur automatique basée sur le contenu */
    max-height: 465px; /* Hauteur maximale pour maintenir la cohérence */
    width: 97%;
    border-radius: 12px;
    transition: opacity 0.3s ease, transform 0.3s ease;
    margin: 0 0 15px 0;
    box-sizing: border-box; /* Inclut les paddings et borders dans la hauteur */
}
.desimg h3 {
    border-top: 1px #5c5a5a solid;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background-color: rgb(1 1 12 / 36%) !important;
    height: 30px !important; /* Hauteur fixe en pixels avec !important */
    min-height: 30px !important; /* Hauteur minimale fixe */
    max-height: 30px !important; /* Hauteur maximale fixe */
    width: 100% !important;
    margin: 0 !important;
    padding: 0 5px !important;
    font-size: 15px !important;
    font-weight: bold !important;
    color: #ffffff !important;
    text-align: center !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05) !important;
    overflow: hidden !important; /* Empêche le débordement du texte */
    text-overflow: ellipsis !important; /* Ajoute des points de suspension si le texte est trop long */
    white-space: nowrap !important; /* Empêche le texte de passer à la ligne */
    flex-shrink: 0 !important; /* Empêche l'élément de se rétrécir */
    flex-grow: 0 !important; /* Empêche l'élément de s'agrandir */
    box-sizing: border-box !important; /* Inclut les paddings et borders dans la hauteur */
}
.ads.white-bg {
    background-color: rgb(243, 5, 5);
}
.desimg span {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-top: 1px hsl(0deg 0% 51.81%) solid !important;
    background-color: rgb(1 1 12 / 36%) !important;
    height: 30px !important; /* Hauteur fixe en pixels avec !important */
    min-height: 30px !important; /* Hauteur minimale fixe */
    max-height: 30px !important; /* Hauteur maximale fixe */
    width: 100% !important;
    font-size: 15px !important;
    font-weight: bold !important;
    color: white !important;
    text-align: center !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    padding: 0 5px !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05) !important;
    overflow: hidden !important; /* Empêche le débordement du texte */
    text-overflow: ellipsis !important; /* Ajoute des points de suspension si le texte est trop long */
    white-space: nowrap !important; /* Empêche le texte de passer à la ligne */
    flex-shrink: 0 !important; /* Empêche l'élément de se rétrécir */
    flex-grow: 0 !important; /* Empêche l'élément de s'agrandir */
    box-sizing: border-box !important; /* Inclut les paddings et borders dans la hauteur */
}

.slide {
    width: 100%;
    height: auto;
    max-height: 350px; /* Réduit légèrement pour laisser plus d'espace pour h3 et span */
    border-radius: 0 0 12px 12px;
    /*border: 1px #5c5a5a solid;*/
    object-fit: contain;
    background-color: #111;
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
    transition: transform 0.3s ease;
    flex-grow: 1; /* Permet à l'image de prendre l'espace disponible */
    flex-shrink: 1; /* Permet à l'image de se rétrécir si nécessaire */
}

/*.slide:hover {
    transform: scale(1.03);
}*/

.slider-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 10px;
    height: 10%;
}

.slider-btn {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #4a6cf7, #2e3b8b);
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 24px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.slider-btn:hover {
    background: linear-gradient(135deg, #2e3b8b, #1a2563);
    transform: translateY(-2px);
    box-shadow: 0 5px 8px rgba(0, 0, 0, 0.3);
}

.slider-btn:active {
    transform: translateY(1px);
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.15);
}
.editbtnn{
    border: none;
    margin: 0; /* Suppression des marges pour un meilleur alignement */
    margin-right: 10px;
    padding: 0px;
    width: 32px; /* Taille fixe pour un cercle parfait */
    height: 32px; /* Même valeur que la largeur pour un cercle parfait */
    color: white;
    font-size: 15px;
    font-weight: bold;
    cursor: pointer;
    border-radius: 50%; /* Cercle parfait */
    /* Icône de stylo (pen) en SVG directement dans le CSS */
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24'%3E%3Cpath fill='white' d='M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z'/%3E%3C/svg%3E");
    background-size: 60%;
    background-repeat: no-repeat;
    background-position: center;
    background-color: #0c0d47;
    border: 2px solid #f5630a;
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    display: flex; /* Pour un meilleur alignement vertical */
    align-items: center;
    justify-content: center;
}
.editbtnn:hover {
    background-color: #32447a;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24'%3E%3Cpath fill='%23f5630a' d='M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z'/%3E%3C/svg%3E");
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}
.menux {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #10102b;
    padding: 8px 8px 0 8px; /* Padding ajusté pour éviter le débordement */
    height: 40px; /* Hauteur fixe au lieu de pourcentage */
    width: 100%;
    position: absolute; /* Position absolue pour ne pas influencer le footer */
    bottom: 0; /* Positionné en bas */
    left: 0; /* Aligné à gauche */
}

.menubtn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    width: 99%;
    border-radius: 10px;
    gap: 5px; /* Réduit l'espace entre les boutons */
}

/* Style pour les boutons du menu */
.menubtn button {
    height: 40px;
    font-size: 16px; /* Taille de police réduite */
    font-weight: bold;
    border: none;
    cursor: pointer;
    color: white;
    padding: 0px;
    margin-left: 2px;
    margin-right: 2px;
    box-shadow: 0 4px 8px rgba(255, 107, 0, 0.2);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    border-radius: 50px;
    background-origin: border-box;
    background-clip: content-box, border-box;
    background-image: linear-gradient(#10102b, #10102b), linear-gradient(to right, #ff5903, #ffb700, #ff5903);
    border: 2px solid transparent; /* Bordure réduite */
    white-space: nowrap; /* Empêche le texte de passer à la ligne */
    overflow: hidden; /* Cache le texte qui dépasse */
    text-overflow: ellipsis; /* Ajoute des points de suspension si le texte est trop long */
}

.menubtn button:hover {
    background: linear-gradient(135deg, #ff6b00, #e65100);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(255, 107, 0, 0.3);
}

.menubtn button:active {
    transform: translateY(1px);
    box-shadow: 0 2px 4px rgba(255, 107, 0, 0.2);
}

/* Indications d'upload */
.upload-indicator {
    display: inline-block;
    background-color: #ff3a30;
    color: white;
    font-size: 12px;
    font-weight: 600;
    padding: 3px 8px;
    border-radius: 6px;
    margin-bottom: 5px;
}

.upload-date {
    display: inline-block;
    background-color: #f0f3ff;
    color: #4a6cf7;
    font-size: 12px;
    font-weight: 500;
    padding: 3px 8px;
    border-radius: 6px;
    margin-bottom: 5px;
}

/* Boutons d'édition de design */
.edit-design-btn {
    background: linear-gradient(135deg, #ff9800, #ff6b00);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 5px;
    box-shadow: 0 2px 5px rgba(255, 107, 0, 0.2);
    transition: all 0.2s ease;
}

.edit-design-btn:hover {
    background: linear-gradient(135deg, #ff6b00, #e65100);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 107, 0, 0.3);
}

.footer {
    background: linear-gradient(128deg, #0b012b, #da7509);
    border-radius: 15px 15px 0 0;
    padding: 10px 0;
    display: flex;
    color: white;
    font-weight: 500;
    font-family: 'Segoe UI', sans-serif;
    font-size: 15px;
    align-items: center;
    justify-content: center;
    height: 10%; /* Hauteur fixe au lieu de pourcentage */
    min-height: 50px; /* Hauteur minimale pour assurer la visibilité */
    width: 100%;
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 20; /* Z-index augmenté pour s'assurer qu'il est au-dessus des autres éléments */
    margin-top: 10px; /* Marge supérieure pour créer de l'espace */
    bottom: 0; /* Ancré au bas de la page */
}

.footer p {
    opacity: 0.9;
    margin: 0;
    font-size: 18px;
    font-weight: bold;
    position: relative;
}

.footer p::before {
    content: '●';
    margin: 0 10px;
    font-size: 10px;
    vertical-align: middle;
    color: #ff9800;
}

.footer p::after {
    content: '●';
    margin: 0 10px;
    font-size: 10px;
    vertical-align: middle;
    color: #ff9800;
}

/* Correction orthographique */
button[value="Remouve Back Ground"] {
    content: "Remove Background";
}

/* Adaptation responsive */
@media (max-width: 992px) {
    .sadr {
        flex-direction: column;
    }

    .category-niches, .plato {
        width: 95%;
        margin: 5px auto;
    }

    .category-niches {
        height: 30%;
    }

    .plato {
        height: 70%;
    }

    .header img {
        margin-left: 20px;
    }

    .date-box {
        margin-right: 10px;
        font-size: 14px;
    }
}

@media (max-width: 1200px) {
    .slider {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
}

@media (max-width: 992px) {
    .slider {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

@media (max-width: 768px) {
    .menubtn {
        flex-wrap: wrap;
    }

    .menubtn button {
        width: 48%;
        margin: 2px;
        font-size: 14px;
        padding: 5px;
    }

    .slider {
        grid-template-columns: repeat(1, minmax(0, 1fr));
    }

    .desimg {
        max-height: none;
    }

    .slide {
        max-height: 300px;
    }
}

/****form add niche ******************************************************************************/
/* Styles communs pour les formulaires déplaçables */
.draggable-form {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 550px;
    height: 380px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: rgba(245, 245, 245, 0.95);
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
    padding: 20px;
    z-index: 1000;
    animation: fadeIn 0.3s ease-out;
    cursor: default;
}

/* En-tête du formulaire */
.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding-bottom: 10px;
    margin-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}
/*******************************************************************************************/
.design-buttons {
    display: flex;
    width: auto; /* Largeur automatique */
    justify-content: space-around; /* Espace entre les boutons */
    align-items: center;
    margin-top: 5px;
    /*gap: 10px; /* Espace fixe entre les boutons */
    border: none;
}

/* Bouton mockup (commenté - utilise maintenant le HTML statique) */
.mockupbtnn {
    /* Styles conservés pour la compatibilité mais le bouton est désactivé */
    border: none;
    padding: 0px;
    width: 32px;
    height: 32px;
    color: white;
    font-size: 15px;
    font-weight: bold;
    cursor: pointer;
    border-radius: 50%;
    /* Icône de t-shirt en SVG directement dans le CSS */
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24'%3E%3Cpath fill='white' d='M16 21H8a1 1 0 0 1-1-1v-7.93l-1.3 1.3a1 1 0 0 1-1.4-1.4l2.82-2.83a1 1 0 0 1 .7-.29H9V7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v1.85h1.18a1 1 0 0 1 .7.29l2.83 2.83a1 1 0 0 1-1.42 1.4L17 12.07V20a1 1 0 0 1-1 1zM9 7v1.85h6V7H9zm6.7 5.31L17 10.93l-2-2H9l-2 2 1.3 1.3a1 1 0 0 1 .3.7V19h6v-6a1 1 0 0 1 .3-.7z'/%3E%3C/svg%3E");
    background-size: 80%;
    background-repeat: no-repeat;
    background-position: center;
    background-color: #0c0d47;
    border: 2px solid #f5630a;
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
}

.mockupbtnn:hover {
    background-color: #32447a;
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24'%3E%3Cpath fill='%23f5630a' d='M16 21H8a1 1 0 0 1-1-1v-7.93l-1.3 1.3a1 1 0 0 1-1.4-1.4l2.82-2.83a1 1 0 0 1 .7-.29H9V7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v1.85h1.18a1 1 0 0 1 .7.29l2.83 2.83a1 1 0 0 1-1.42 1.4L17 12.07V20a1 1 0 0 1-1 1zM9 7v1.85h6V7H9zm6.7 5.31L17 10.93l-2-2H9l-2 2 1.3 1.3a1 1 0 0 1 .3.7V19h6v-6a1 1 0 0 1 .3-.7z'/%3E%3C/svg%3E");
}
/*******mockup design (commenté - utilise maintenant le HTML statique) *******************************/
/*
   Les styles ci-dessous sont conservés pour référence mais le formulaire mockup
   est maintenant géré directement dans le HTML avec des attributs inline
*/

/* Styles pour la modal de mockup */
.mockup-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7); /* Fond semi-transparent */
    z-index: 1000; /* Valeur élevée pour être au-dessus de tout */
    display: flex;
    justify-content: center;
    align-items: center;
}

.mockup-container {
    width: 95%;
    max-width: 1100px; /* Plus large */
    background-color: #f5f5f5; /* Fond gris très clair, presque blanc */
    border-radius: 10px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
}

.mockup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px; /* Hauteur réduite */
    background-color: #ff6b00; /* Orange vif */
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.mockup-header h2 {
    margin: 0;
    color: white;
    font-size: 20px;
    font-weight: 600; /* Semi-gras */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); /* Légère ombre pour meilleure lisibilité */
}

.mockup-close {
    background: none;
    border: none;
    color: white;
    font-size: 28px;
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.mockup-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.mockup-content {
    display: flex;
    flex-direction: column;
    padding: 15px;
    align-items: center;
}

.mockup-layout {
    display: flex;
    flex-direction: row; /* Disposition horizontale */
    width: 100%;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;
}

.mockup-footer {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 10px; /* Marge réduite */
}

.demockup-button {
    background-color: #ff6b00;
    color: white;
    border: none;
    padding: 8px 20px; /* Taille réduite */
    font-size: 14px; /* Taille de police réduite */
    font-weight: bold;
    border-radius: 30px; /* Bouton arrondi */
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    text-transform: uppercase; /* Texte en majuscules */
    letter-spacing: 1px; /* Espacement des lettres */
}

.demockup-button:hover {
    background-color: #ff8c00;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.demockup-button:active {
    transform: translateY(1px);
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.2);
}

.tshirt-preview {
    position: relative;
    width: 450px; /* Plus grand */
    height: 450px; /* Moins haut */
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 0; /* Pas de marge en bas */
    overflow: hidden;
    border-radius: 8px;
    background-color: transparent; /* Fond transparent pour une meilleure fidélité des couleurs */
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); /* Légère ombre pour délimiter la zone */
    transition: all 0.3s ease; /* Transition douce pour les changements */
}

#tshirt-base, #static-tshirt-base {
    position: absolute;
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: all 0.3s ease;
}

#tshirt-design, #static-tshirt-design {
    position: absolute;
    width: 40%; /* Taille réduite pour un meilleur placement */
    height: auto;
    z-index: 10;
    top: 30%; /* Ajusté pour un meilleur placement */
    left: 30%; /* Ajusté pour un meilleur placement */
    pointer-events: none;
    mix-blend-mode: screen; /* Pour un meilleur rendu sur t-shirt foncé */
    transition: all 0.3s ease;
    max-width: 200px; /* Limite la taille maximale */
}

.color-options {
    width: 100%;
    max-width: 400px; /* Moins large */
    background-color: rgba(0, 0, 0, 0.03); /* Fond légèrement grisé */
    padding: 15px;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.1); /* Bordure subtile */
}

.color-options p {
    color: #333333; /* Texte gris foncé */
    font-size: 16px;
    margin-top: 0;
    margin-bottom: 10px;
    font-weight: 500; /* Légèrement plus gras */
}

.color-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 8px; /* Espacement réduit pour plus de compacité */
    justify-content: center;
    margin-top: 10px;
    padding: 5px; /* Padding autour de la grille */
    background-color: transparent; /* Fond transparent */
}

.color-option {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    cursor: pointer;
    border: none;
    transition: transform 0.2s ease;
    margin: 0 auto;
    box-shadow: none;
    background-color: transparent !important;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    padding: 0;
}

.tshirt-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: contain;
    display: block;
    border-radius: 8px;
}

.color-option:hover {
    transform: scale(1.1);
    z-index: 1;
}

.color-option.active {
    outline: 2px solid #ff6b00; /* Utiliser outline au lieu de border */
    transform: scale(1.05);
}

/* Style pour les miniatures de t-shirts */
.color-option {
    overflow: visible; /* Permettre à l'image de déborder légèrement si nécessaire */
    position: relative; /* Pour le positionnement des éléments enfants */
}

@media (max-width: 768px) {
    .mockup-container {
        width: 98%;
    }

    .mockup-layout {
        flex-direction: column;
        align-items: center;
    }

    .tshirt-preview {
        width: 350px;
        height: 350px;
    }

    .color-options {
        max-width: 100%;
    }

    .color-grid {
        grid-template-columns: repeat(6, 1fr);
        gap: 6px;
    }

    .color-option {
        width: 32px;
        height: 32px;
    }
}

@media (max-width: 480px) {
    .tshirt-preview {
        width: 280px;
        height: 280px;
    }

    .color-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}
/***********************************************************************************************************/
/* Zone déplaçable */
.draggable-header {
    flex-grow: 1;
    cursor: move; /* Indique que l'élément est déplaçable */
    padding: 5px;
}

.draggable-header h2 {
    margin: 0;
    padding: 0;
}

/* Bouton de fermeture */
.close-btn {
    cursor: pointer;
    transition: transform 0.2s ease;
    margin-left: 10px;
}

.close-btn:hover {
    transform: scale(1.1);
}

/* Style pour les formulaires en cours de déplacement */
.draggable-form.dragging {
    opacity: 0.9;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
    transition: none; /* Désactiver les transitions pendant le déplacement */
}

/* Style pour la notification de copie dans le presse-papier */
.clipboard-notification {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(100px);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 14px;
    z-index: 9999;
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    max-width: 80%;
    text-align: center;
    pointer-events: none;
}

.clipboard-notification.show {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
}

.addnichfrm-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: #10102b;
    color: white;
    font-size: 18px;
    font-weight: 600;
    padding: 20px;
    margin-left: 8px;
    border-radius: 16px;
    width: 90%;
    height: 90%;
    text-align: center;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

/* Champ de saisie et boutons */
input {
    width: 92%;
    height: 25px;
    padding: 12px;
    background-color: rgb(246, 249, 249);
    border: 1px solid #ff6b00;
    color: #333;
    font-family: 'Segoe UI', sans-serif;
    font-weight: bold;
    font-size: 18px;
    border-radius: 8px;
    transition: all 0.2s ease;
}

input:focus {
    outline: none;
    border: 2px solid #ff6b00;
    box-shadow: 0 0 8px rgba(255, 107, 0, 0.4);
    transform: translateY(-2px);
}

.addnichfrm-box button {
    padding: 10px 15px;
    font-family: 'Segoe UI', sans-serif;
    font-weight: bold;
    font-size: 18px;
    border-radius: 8px;
    margin-top: 36px;
    width: 232px;
    height: 45px;
    background-color: white;
    cursor: pointer;
    border: none;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.addnichfrm-box button:hover {
    background-color: #ff6b00;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(255, 107, 0, 0.2);
}
#save-niche:hover {
    background-color: rgb(5, 222, 12);
    color: white;
}
#nicheCategory {
    width: 98%;
    padding: 12px;
    margin: 10px 0;
    background-color: rgb(17 7 120 / 93%);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    font-family: 'Segoe UI', sans-serif;
    font-weight: bold;
    font-size: 18px;
    border-radius: 8px;
    transition: all 0.2s ease;
}

#nicheCategory {
    height: auto;
}

#nicheCategorys, #nicheName {
    width: 54%;
    padding: 12px;
    margin: 10px 0;
    background-color: rgb(17 7 120 / 93%);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    font-family: 'Segoe UI', sans-serif;
    font-weight: bold;
    font-size: 18px;
    border-radius: 8px;
    transition: all 0.2s ease;
}
#nicheCategorys {
  margin-bottom: 0;
  margin-top: 19px;
}
#nicheCategory:focus, #nicheCategorys:focus, #nicheName:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.6);
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
}

/*********** edit niche ******************************************************************************************************************/
#editnichfrm {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 500px;
    height: 450px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: rgba(245, 245, 245, 0.95);
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
    padding: 20px;
    z-index: 1000;
    animation: fadeIn 0.3s ease-out;
}
.clsee {
    height: 30px;
    width: 30px;
    position: absolute;
    border-radius: 50px;
    right: -1px;
    top: 0px;
 }
 .clseee {
    height: 30px;
    width: 30px;
    border-radius: 50px;
    position: absolute;
    right: 1px;
    top: 1px;
}
.editnichfrm-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: #10102b;
    color: white;
    font-size: 18px;
    font-weight: 600;
    padding: 25px;
    border-radius: 16px;
    width: 90%;
    height: 90%;
    text-align: center;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.editnichfrm-box button {
    padding: 10px 15px;
    font-family: 'Segoe UI', sans-serif;
    font-weight: bold;
    font-size: 18px;
    border-radius: 8px;
    margin: 7px 5px;
    width: 200px;
    height: 45px;
    background-color: white;
    cursor: pointer;
    border: none;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.editnichfrm-box button:hover {
    background-color: #ff6b00;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(255, 107, 0, 0.2);
}

#nichname {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px 8px 0 0;
    padding: 15px;
    width: 90%;
    height: 40px;
    font-family: 'Segoe UI', sans-serif;
    font-weight: bold;
    font-size: 18px;
}

#modifname {
    background-color: white;
    color: #4a6cf7;
    margin-top: 17px;
}

#modifname:hover {
    background-color: #09ee0d;
    color: white;
}

#deletenich {
    background-color: white;
    color: #e53935;
}

#deletenich:hover {
    background-color: #e53935;
    color: white;
}

#movetoarchiv {
    background-color: white;
    color: #546e7a;
}

#movetoarchiv:hover {
    background-color: #546e7a;
    color: white;
}
/*********** frm edit design *******************************************************************************************************/
#editdesignfrm {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 500px;
    height: 550px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: rgba(245, 245, 245, 0.95);
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
    padding: 20px;
    z-index: 1000;
    animation: fadeIn 0.3s ease-out;
}
.idi {
    margin: 0;
    margin-bottom: 10px;
 }
.clse {
    height: 30px;
    width: 30px;
    border-radius: 50px;
    position: absolute;
    right: 0px;
    top: -3px;
 }
.edtitl {
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
    align-items: center;
    width: 100%;
    height: 10%;
}
#adss {
    background-color: white;
    color: #e31010;
    width: 16%;
    height: 80%;
    font-size: 15px;
    font-weight: bold;
    cursor: pointer;
}
#noads {
    background-color: white;
    color: #e31010;
    width: 30%;
    height: 80%;
    font-size: 15px;
    font-weight: bold;
    cursor: pointer;
}
#pladi {
    background-color: white;
    color: #e31010;
    width: 22%;
    height: 80%;
    font-size: 15px;
    font-weight: bold;
    cursor: pointer;
}
#pladish {
    background-color: white;
    color: green;
    width: 45%;
    height: 80%;
    font-size: 15px;
    font-weight: bold;
    cursor: pointer;
}
#pladish:hover {
    background-color: green;
    color: white;
}
#pladi:hover {
    background-color: red;
    color: white;
}
.editdesignfrm-box {
    background: #10102b;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: white;
    font-size: 18px;
    font-weight: 600;
    padding: 25px;
    border-radius: 16px;
    width: 90%;
    height: 90%;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}
.editdesignfrm-box button {
    padding: 10px 15px;
    font-family: 'Segoe UI', sans-serif;
    font-weight: bold;
    font-size: 18px;
    border-radius: 8px;
    margin:5px;
    width: 200px;
    height: 45px;
    background-color: white;
    cursor: pointer;
    border: none;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

#ttl, #brnd {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    margin-top: 10px;
    width: 90%;
    padding: 12px;
    border-radius: 8px 8px 0 0;
    color: white;
    font-family: 'Segoe UI', sans-serif;
    font-weight: bold;
    font-size: 18px;
}
.inputz {
    background-color: white;
    margin: 0;
    margin-bottom: 3px;
    height: 20px;
    width: 90%;
    border-radius: 0 0 8px 8px;
    color: #333;
    border: none;
    padding: 12px;
    font-family: 'Segoe UI', sans-serif;
    font-weight: bold;
    font-size: 18px;
}

#savechange {
    background-color: white;
    color: #4a6cf7;
    margin-top: 15px;
}

#savechange:hover {
    background-color: #06e315;
    color: white;
}

#deletedes {
    background-color: white;
    color: #e53935;
}

#deletedes:hover {
    background-color: #e53935;
    color: white;
}

#movedestorchiv {
    background-color: white;
    color: #546e7a;
}

#movedestorchiv:hover {
    background-color: #546e7a;
    color: white;
}

/********* frm upload design ***************************************************************************************************/
#uploaddesfrm {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 775px;
    height: 583px;
    padding: 5px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: rgba(245, 245, 245, 0.95);
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
    z-index: 1000;
    animation: fadeIn 0.3s ease-out;
}
.clsex {
    height: 25px;
    width: 25px;
    border-radius: 50px;
    position: absolute;
    right: 5px;
    top: 1px;
 }
.uploaddesfrm-box {
    background: #10102b;
    display: flex;
    flex-direction: column;
    margin-left: 18px;
    margin-top: 11px;
    align-items: center;
    color: white;
    font-size: 18px;
    font-weight: 600;
    border-radius: 25px;
    width: 95%;
    height: 96%;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}
.corddes {
    display: flex;
    justify-content: center;
    width: 95%;
    gap: 35px;
    height: 81%;
    border-radius: 8px;
    color: white;
    margin-bottom: 20px;
}
#editnichname {
    width: 91%;
    height: 25px;
    padding: 12px;
    background-color: white;
    border: none;
    color: #333;
    font-family: 'Segoe UI', sans-serif;
    font-weight: bold;
    font-size: 18px;
    border-radius: 0 0 8px 8px;
    margin-bottom: 15px;
}
.iddes {
    border: 1px solid white;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    gap: 10px;
    margin-bottom: 5px;
    width: 98%;
    height: 100%;
    border-radius: 12px;
    color: white;
    background-color: #10102b;
}
#uploadfrompcall {
    background: linear-gradient(135deg, #ff9800, #ff6b00);
    color: white;
    font-size: 20px;
    font-weight: 600;
    height: 18%;
    width: 54%;
    border-radius: 12px;
    border: none;
    box-shadow: 0 4px 15px rgba(255, 107, 0, 0.3);
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: 21px;
    margin-bottom: -9px;
}
#uploadfrompc:hover {
    background: linear-gradient(135deg, #ff6b00, #e65100);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 0, 0.4);
}
#uploadfrompcall:hover {
    background: linear-gradient(135deg, #ff6b00, #e65100);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 0, 0.4);
}
.btnd {
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
    margin-bottom: 11px;
    border-radius: 10px;
    width: 100%;
    height: 10%;
}
.btnd button {
    padding: 10px 15px;
    font-family: 'Segoe UI', sans-serif;
    font-weight: bold;
    font-size: 21px;
    border-radius: 8px;
    background-color: white;
    cursor: pointer;
    border: none;
    margin-top: 4px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    width: 20%;
    height: 107%;
}
#savechangee {
    color: #4a6cf7;
}
#savechangee:hover {
    background-color: #07ef13;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(74, 108, 247, 0.2);
}
/***********sarot key****************************************************************************************************************
/* Animation de fondu */
@keyframes fadeIn {
    from { opacity: 0; transform: translate(-50%, -60%); }
    to { opacity: 1; transform: translate(-50%, -50%); }
  }

  /* Overlay pour bloquer les interactions avec le reste de la page */
  .overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
  }

  /* Styles pour le conteneur principal */
  #sarot {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    box-shadow: inset 10px 10px 25px blue;
    width: 800px;
    height: 270px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: linear-gradient(128deg, #8e0606, #f00f04);
    border-radius: 18px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
    padding: 20px;
    z-index: 1000;
    transition: opacity 0.3s ease, top 0.3s ease;
  }


  .sdoo {
    height: 30px;
    width: 32px;
    border-radius: 6px;
    position: absolute;
    background-color: white;
    color: white;
    right: 8px;
    top: 9px;
 }

  /* Structure interne */
  .srt {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
     width: 99%;
  }
  /* Section d'information */
  .infos {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
    width: 80%;
    padding: 7px;
    border-radius: 10px;
  }


  /* Style pour le bouton d'activation large */
  .activation-btn {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    color: white;
    border: 3px solid white;
    font-size: 20px;
    font-weight: bold;
    padding: 12px 30px;
    border-radius: 30px;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 80%;
    text-align: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
    display: inline-block;
    text-decoration: none;
  }

  .activation-btn:before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.6s ease;
  }

  .activation-btn:hover {
    background: linear-gradient(135deg, #2E7D32, #1B5E20);
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
    color: white;
    text-decoration: none;
  }

  .activation-btn:hover:before {
    left: 100%;
  }

  .activation-btn:active {
    transform: translateY(1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
  .titresrt {
    font-size: 25px;
    font-weight: bold;
    color: white;
    padding-bottom: 3px;
  }

  /* Section pour les champs et boutons */
  .validih {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 80%;
    border-radius: 10px;
    padding: 5px;
  }
  .btnv {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    width: 100%;
  }
  /* Champ de recherche */
  #search {
    width: 99%;
    padding: 10px 15px;
    margin-bottom: 15px;
    border: 1px solid #ed0606;
    background-color: hsl(260deg 29.34% 98.66%);
    font-weight: bold;
    font-size: 18px;
    margin-top: -3px;
    border-radius: 25px;
    transition: border 0.3s ease;
  }

  #search:focus {
    border-color: #4285f4;
    outline: none;
    box-shadow: 0 0 5px rgba(66, 133, 244, 0.3);
  }
#valid {
    background-color: #960505;
    color: white;
    border: 4px white solid;
    font-size: 18px;
    font-weight: bold;
    padding: 10px 20px;
    margin-top: 13px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 26%;
}
#valid:hover {
    background-color: #3b77db;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
  /* Styles supplémentaires pour le responsive */
  /* Responsive Design */
  @media (max-width: 768px) {
    .srt {
      flex-direction: column;
    }

    .infos, .validih {
      width: 100%;
      align-items: center;
      padding: 10px 0;
    }

    #sarot {
      height: auto;
      min-height: 300px;
    }

    .contact-btn {
      width: 80%;
      justify-content: center;
    }
  }