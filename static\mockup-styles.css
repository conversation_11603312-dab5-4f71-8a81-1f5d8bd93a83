/* Styles pour la modal de mockup */
.mockup-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.mockup-container {
    /*background-color: white;*/
    border: 4px solid #f39553;
    width: 95%;
    max-width: 1200px; /* Augmenté pour plus d'espace */
    max-height: 90vh; /* Limiter la hauteur à 90% de la hauteur de la fenêtre */
    border-radius: 8px;
    overflow: hidden;
    /*box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);*/
}

.mockup-header {
    background-color: #f39553;
    color: white;
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.mockup-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.mockup-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
}

.mockup-content {
    padding: 10px;
}

.mockup-layout {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: flex-start; /* Aligner les éléments en haut */
}

.tshirt-preview {
    flex: 1.5; /* Augmenté pour donner plus d'espace au t-shirt */
    min-width: 400px; /* Augmenté pour un affichage plus grand */
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 700px; /* Hauteur fixe pour s'assurer que tout le t-shirt est visible */
}

.color-options {
    border:4px solid #f39553;
    position: relative;
    top: 200px;
    flex: 1;
    min-width: 300px;
    border-radius: 20px;
}

.color-grid {
    background-color:white;
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 8px;
    margin-top: 6px;
    max-height: 500px; /* Augmenté pour s'adapter à la nouvelle taille */
    overflow-y: auto;
    padding: 5px;
    border: 1px solid #f0f2f5;
    border-radius: 5px;
}

/* Style pour les nouvelles div de couleur */
.color-option {
    width: 100%;
    aspect-ratio: 1;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    transition: transform 0.2s;
    background-color: #f5f5f5; /* Couleur de fond par défaut */
    min-height: 40px;
}

.color-option:hover {
    transform: scale(1.05);
    border-color: #ff6600;
}

/* Ancien style pour les thumbnails (conservé pour compatibilité) */
.tshirt-thumbnail {
    width: 100%;
    aspect-ratio: 1;
    object-fit: cover;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    transition: transform 0.2s;
}

.tshirt-thumbnail:hover {
    transform: scale(1.05);
    border-color: #ff6600;
}

.mockup-footer {
    margin-top: 20px;
    text-align: center;
}

.demockup-button {
    background-color: #f39553;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
}

.demockup-button:hover {
    background-color: #e55c00;
}

/* Style pour le conteneur du design */
#static-design-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 50% !important;
    height: 50% !important;
    position: absolute;
    top: 30% !important;  /* Positionné encore plus haut sur le t-shirt */
    left: 35% !important; /* Centré horizontalement */
    transform: translate(-50%, -50%) !important;
}

/* Style pour l'image du design */
#static-tshirt-design {
    display: block;
    max-width: 100% !important;
    max-height: 100% !important;
    width: 100% !important;
    height: auto !important;
    object-fit: contain;
}
