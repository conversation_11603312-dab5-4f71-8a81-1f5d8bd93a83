
// DOM Utility functions
const DOM = {
    // Get element by ID with null check
    getById: (id) => {
        const element = document.getElementById(id);
        if (!element) console.warn(`Element with ID "${id}" not found`);
        return element;
    },

    // Get elements by selector with null check
    getAll: (selector, parent = document) => {
        const elements = parent.querySelectorAll(selector);
        if (elements.length === 0) console.warn(`No elements found with selector "${selector}"`);
        return elements;
    },

    // Create an element with attributes and classes
    create: (tag, attributes = {}, classes = []) => {
        const element = document.createElement(tag);
        Object.entries(attributes).forEach(([key, value]) => element.setAttribute(key, value));
        if (classes.length) element.classList.add(...classes);
        return element;
    }
};
 //********************************************************************* uploaddesfrm / uploadesign
 //*************************** */
 document.addEventListener("DOMContentLoaded", function() {
    const editNicheForm = document.getElementById("editnichfrm"); //edit niche frm
    const backButton = document.getElementById("bak");
    const frmeditdes = document.getElementById("editdesignfrm"); // edit design frm
    const btnclosfrm = document.getElementById("bakk");
    const frmupdes = document.getElementById("uploaddesfrm"); // uploaddesign frm
    const btnopfrmup = document.getElementById("uploadesign");
    const btnclosfrmup = document.getElementById("bakke");
    const modifNameBtn = document.getElementById('modifname');
    const deleteNicheBtn = document.getElementById('deletenich');
    const moveToArchiveBtn = document.getElementById('movetoarchiv');
    const saveChangeBtn = document.getElementById('savechange');
    const deleteDesignBtn = document.getElementById('deletedes');
    const moveToArchivdes = document.getElementById('movedestorchiv');
    if (backButton && editNicheForm) {
        backButton.addEventListener("click", function() {
            editNicheForm.style.display = "none";
        });
    }
    moveToArchivdes.addEventListener("click", function() {
        moveDesignToArchive();
    });
    deleteDesignBtn.addEventListener("click", function() {
        deleteDesign();
    });
    btnclosfrm.addEventListener("click", function() {
        frmeditdes.style.display = "none";
    });
    btnopfrmup.addEventListener("click", function() {
        frmupdes.style.display = "block";
    });
    btnclosfrmup.addEventListener("click", function() {
        frmupdes.style.display = "none";
    });
    modifNameBtn.addEventListener("click", function() {
         changeNicheName();
    });
    deleteNicheBtn.addEventListener("click", function() {
        deleteNiche();
    });
    moveToArchiveBtn.addEventListener("click", function() {
        moveNicheToArchive();
    });
    saveChangeBtn.addEventListener("click", function() {
        updateDesignDetails();
    });
});
/*****************date time ************************************************** */
const DateTimeManager = {
    updateDateTime: () => {
        const dateBox = DOM.getById('liveDateTime');
        if (!dateBox) return;

        const now = new Date();
        const options = {
            weekday: 'long',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
        };

        const formattedDateTime = now.toLocaleString('en-US', options).replace(',', '');
        dateBox.textContent = formattedDateTime;
    },

    init: () => {
        DateTimeManager.updateDateTime();
        setInterval(DateTimeManager.updateDateTime, 1000);
    }
};
//***************upload imj to rmvbgd************************************************************************* */
//************btn download ******************************************************************************************* */
//************************************************************************************************************ */
// Simple Slider functionality avec défilement natif
const SimpleSlider = {
    slider: null,
    slides: [],

    init: function() {
        this.slider = document.querySelector('.slider');
        if (!this.slider) return;

        this.slides = document.querySelectorAll('.slide');
        console.log("Simple slider avec défilement natif initialisé");
    }
};
// Category and Niche Management
const NicheManager = {
    // Load niches for a specific category
    loadNichesForCategory: function(categoryName) {
        const h2Categ = document.querySelector('h2.categ');
        const h2Titro = document.querySelector('h2.titro');
        if (h2Categ) h2Categ.textContent = categoryName;
        if (h2Titro) h2Titro.textContent = "";
        document.querySelector('.slider').innerHTML = '';
        // Call API to get niches
        fetch(`/api/niches_by_category/${categoryName}`)
            .then(response => response.json())
            .then(niches => {
                // Empty the niches list
                const listNiches = document.querySelector('.listniches');
                if (!listNiches) return;
                listNiches.innerHTML = '';

                // If no niche found
                if (niches.length === 0) {
                    listNiches.innerHTML = '<div>Aucune niche trouvée pour cette catégorie</div>';
                    return;
                }

                // Add each niche to the list
                niches.forEach(niche => {
                    const nicheDiv = DOM.create('div', {'data-niche-id': niche.id}, ['niche-item']);
                    nicheDiv.innerHTML = `
                       ${niche.name}
                       <button class="edit-button"></button>
                    `;
                    // Add event listener for niche click
                    nicheDiv.addEventListener('click', function() {
                        const nicheId = this.getAttribute('data-niche-id');
                        const nicheName = this.textContent.replace('Edit', '').trim();
                        // Update niche title
                        const titleElement = document.querySelector('h2.titro');
                        if (titleElement) titleElement.textContent = nicheName;
                        // Load designs for this niche
                        DesignManager.loadDesignsForNiche(nicheId, nicheName);
                    });
                    listNiches.appendChild(nicheDiv);
                });
                // Add event listeners for edit buttons
                document.querySelectorAll('.edit-button').forEach(button => {
                    button.addEventListener('click', function(event) {
                        // Prevent click propagation to parent
                        event.stopPropagation();

                        // Get niche ID from parent element
                        const nicheId = this.parentElement.getAttribute('data-niche-id');
                        NicheManager.editNiche(nicheId);
                    });
                });
            })
            .catch(error => {
                console.error('Erreur lors du chargement des niches:', error);
                const listElement = document.querySelector('.listniches');
                if (listElement) listElement.innerHTML = '<div>Erreur lors du chargement des niches</div>';
            });
    },
    // Edit niche functionality*****************************************************************************************
    editNiche: function(nicheId) {
        console.log(`Ouverture du formulaire pour la niche avec l'ID: ${nicheId}`);
        // Reference to edit form
        const editNicheForm = DOM.getById('editnichfrm');
        if (!editNicheForm) return;
        // Store niche ID in data attribute
        editNicheForm.setAttribute('data-niche-id', nicheId);
        // Récupérer le nom de la niche
        const nicheElement = document.querySelector(`.niche-item[data-niche-id="${nicheId}"]`);
        let nicheName = "Niche inconnue";
        if (nicheElement) {
            // Extraire uniquement le texte (sans le texte du bouton Edit)
            nicheName = nicheElement.textContent.replace('Edit', '').trim();
        }
        // Mettre à jour le label avec le nom de la niche
        const nicheNameLabel = document.getElementById('nichname');
        if (nicheNameLabel) {
            nicheNameLabel.textContent = nicheName;
        }
        // Show form
        editNicheForm.style.display = 'block';
    },
    // Initialize category buttons*************************************************************************************
    initCategoryButtons: function() {
        const btnEvergreen = DOM.getById('btnevergreen');
        const btnEvent = DOM.getById('btnevent');
        const btnOther = DOM.getById('btnother');
        if (btnEvergreen) {
            btnEvergreen.addEventListener('click', () => this.loadNichesForCategory('Evergreen'));
        }
        if (btnEvent) {
            btnEvent.addEventListener('click', () => this.loadNichesForCategory('Event'));
        }
        if (btnOther) {
            btnOther.addEventListener('click', () => this.loadNichesForCategory('Other Niches Trend'));
        }
    }
};
//*************modif nom niche******************************************************* ****************************************************/
function changeNicheName() {
    const editNicheForm = document.getElementById('editnichfrm');
    if (!editNicheForm) return;

    const nicheId = editNicheForm.getAttribute('data-niche-id');
    const newNicheName = document.getElementById('editnichname').value.trim();
    const currentCategoryName = document.querySelector('h2.categ').textContent;

    if (!nicheId) {
        console.error("ID de niche non trouvé");
        return;
    }

    if (!newNicheName) {
        alert("Veuillez entrer un nouveau nom pour la niche");
        return;
    }

    // Préparation des données pour l'API
    const data = {
        id: nicheId,
        new_name: newNicheName,
        category_name: currentCategoryName
    };

    // Appel à l'API pour modifier le nom
    fetch('/api/update_niche_name', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error("Erreur lors de la modification du nom");
        }
        return response.json();
    })
    .then(result => {
        // Mise à jour de l'interface
        const nicheElement = document.querySelector(`.niche-item[data-niche-id="${nicheId}"]`);
        if (nicheElement) {
            // Mettre à jour le texte (en conservant le bouton Edit)
            const editButton = nicheElement.querySelector('.edit-button');
            nicheElement.textContent = newNicheName + ' ';
            nicheElement.appendChild(editButton);
        }

        // Mettre à jour le label
        const nicheNameLabel = document.getElementById('nichname');
        if (nicheNameLabel) {
            nicheNameLabel.textContent = newNicheName;
        }

        // Fermer le formulaire
        editNicheForm.style.display = "none";

        // Rafraîchir la liste des niches pour cette catégorie
        if (typeof NicheManager !== 'undefined' && NicheManager.loadNichesForCategory) {
            NicheManager.loadNichesForCategory(currentCategoryName);
        } else {
            // Si NicheManager n'est pas disponible, rechargez manuellement
            location.reload();
        }

        alert("Nom de la niche modifié avec succès!");
    })
    .catch(error => {
        console.error("Erreur:", error);
        alert("Erreur lors de la modification du nom de la niche. Veuillez réessayer.");
    });
}

//****************supprimer niche******************************************************************************************* ****************
function deleteNiche() {
    const editNicheForm = document.getElementById('editnichfrm');
    if (!editNicheForm) return;

    const nicheId = editNicheForm.getAttribute('data-niche-id');
    const nicheName = document.getElementById('nichname').textContent;
    const currentCategoryName = document.querySelector('h2.categ').textContent;

    if (!nicheId) {
        console.error("ID de niche non trouvé");
        return;
    }

    // Demander confirmation avant de supprimer
    if (!confirm(`Êtes-vous sûr de vouloir supprimer la niche "${nicheName}" ? Cette action est irréversible.`)) {
        return; // L'utilisateur a annulé
    }

    // Préparation des données pour l'API
    const data = {
        id: nicheId,
        niche_name: nicheName,
        category_name: currentCategoryName
    };

    // Appel à l'API pour supprimer la niche
    fetch('/api/delete_niche', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error("Erreur lors de la suppression de la niche");
        }
        return response.json();
    })
    .then(result => {
        // Fermer le formulaire
        editNicheForm.style.display = "none";

        // Rafraîchir la liste des niches pour cette catégorie
        if (typeof NicheManager !== 'undefined' && NicheManager.loadNichesForCategory) {
            NicheManager.loadNichesForCategory(currentCategoryName);
        } else {
            // Si NicheManager n'est pas disponible, rechargez manuellement
            location.reload();
        }

        alert("La niche a été supprimée avec succès!");
    })
    .catch(error => {
        console.error("Erreur:", error);
        alert("Erreur lors de la suppression de la niche. Veuillez réessayer.");
    });
}
//*********** deplacer vers archive ************************************************************************************************* */
function moveNicheToArchive() {
    const editNicheForm = document.getElementById('editnichfrm');
    if (!editNicheForm) return;

    const nicheId = editNicheForm.getAttribute('data-niche-id');
    const nicheName = document.getElementById('nichname').textContent;
    const currentCategoryName = document.querySelector('h2.categ').textContent;

    if (!nicheId) {
        console.error("ID de niche non trouvé");
        return;
    }

    // Demander confirmation avant d'archiver
    if (!confirm(`Êtes-vous sûr de vouloir déplacer la niche "${nicheName}" vers les archives ?`)) {
        return; // L'utilisateur a annulé
    }

    // Préparation des données pour l'API
    const data = {
        id: nicheId,
        niche_name: nicheName,
        category_name: currentCategoryName
    };

    // Appel à l'API pour archiver la niche
    fetch('/api/archive_niche', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error("Erreur lors de l'archivage de la niche");
        }
        return response.json();
    })
    .then(result => {
        // Fermer le formulaire
        editNicheForm.style.display = "none";

        // Rafraîchir la liste des niches pour cette catégorie
        if (typeof NicheManager !== 'undefined' && NicheManager.loadNichesForCategory) {
            NicheManager.loadNichesForCategory(currentCategoryName);
        } else {
            // Si NicheManager n'est pas disponible, rechargez manuellement
            location.reload();
        }

        alert("La niche a été déplacée vers les archives avec succès!");
    })
    .catch(error => {
        console.error("Erreur:", error);
        alert("Erreur lors de l'archivage de la niche. Veuillez réessayer.");
    });
}

//*********************************************************************************************************************** */
// Design Management
const DesignManager = {
    // Load designs for a specific niche
    loadDesignsForNiche: function(nicheId, nicheName) {
        // Update niche title
        const h2Titro = document.querySelector('h2.titro');
        if (h2Titro) h2Titro.textContent = nicheName;

        // Call API to get designs for the niche
        fetch(`/api/designs_by_niche/${nicheId}`)
            .then(response => response.json())
            .then(designs => {
                console.log("Designs récupérés:", designs);

                // DEBUG : Vérifier explicitement les valeurs ads
                designs.forEach(d => {
                    console.log(`Design ${d.id} - ads value: '${d.ads}'`, d);
                });

                // Get slider container
                const sliderContainer = document.querySelector('.slider');
                if (!sliderContainer) return;
                sliderContainer.innerHTML = '';

                // If no design found
                if (designs.length === 0) {
                    sliderContainer.innerHTML = '<div>Aucun design trouvé pour cette niche</div>';
                    return;
                }

                // Add each design to the slider
                designs.forEach(design => {
                    const designDiv = DOM.create('div', {}, ['desimg']);

                    // Determine status
                    const uploadStatus = design.status === 'uploaded' ? 'Uploaded' : 'Not Uploaded';

                    // Process creation date
                    let dateCreated = 'Not uploaded yet';
                    if (design.created_at && design.created_at !== null) {
                        try {
                            const date = new Date(design.created_at);
                            if (!isNaN(date.getTime())) {
                                const year = date.getFullYear().toString().slice(-2);
                                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                                const day = date.getDate().toString().padStart(2, '0');
                                dateCreated = `Date Created  ${year}/${month}/${day}`;
                            }
                        } catch (e) {
                            console.error("Erreur lors du traitement de la date:", e);
                            dateCreated = `DateCreated:  ${design.created_at}`;
                        }
                    }

                    // Get design brand
                    const brandName = design.brand || 'Unknown Brand';

                    // SOLUTION : Afficher correctement ads
                    let adsContent = '';
                    let adsClass = 'ads';

                    // Comparer en tant que chaîne au cas où
                    const adsValue = String(design.ads || '');

                    if (adsValue === 'ads') {
                        adsContent = 'Ads';
                        adsClass = 'ads';
                    } else if (adsValue === 'noads') {
                        adsContent = '';
                        adsClass = 'adsoho';
                    }else {
                        // Valeur par défaut si aucune correspondance
                        adsClass = 'adsoho';
                    }

                    // DEBUG : Afficher dans la console pour vérification
                    console.log(`Design ${design.id} - adsValue: '${adsValue}', adsContent: '${adsContent}', adsClass: '${adsClass}'`);

                    // Build design HTML avec style explicite pour debug
                    designDiv.innerHTML = `
                    <div class="dtup">
                       <div class="uplod">${uploadStatus}</div>
                       <div class="dt">${dateCreated}</div>
                       <div class="${adsClass}" data-ads-value="${adsValue}">${adsContent}</div>
                    </div>
                    <div class="image-container">
                      <img src="/${design.file_path}" alt="${design.title}" class="slide" data-design-id="${design.id}">
                      <div class="hover-controls">
                        <input type="checkbox" class="chqbox" name="uploadSelect" data-design-id="${design.id}">
                        <button class="editbtnn" data-design-id="${design.id}"></button>
                        <button class="mockupbtnn" data-design-id="${design.id}" title="Voir le design sur un t-shirt"></button>
                      </div>
                    </div>
                    <h3>${design.title}</h3>
                    <span>${brandName}</span>
                    `;

                    sliderContainer.appendChild(designDiv);
                });

                document.querySelectorAll('.editbtnn').forEach(button => {
                    button.addEventListener('click', function() {
                        const designId = this.getAttribute('data-design-id');
                        // Afficher le formulaire d'édition de design
                        const editDesignForm = document.getElementById('editdesignfrm');
                        if (editDesignForm) {
                            // Stocker l'ID du design à éditer
                            editDesignForm.setAttribute('data-design-id', designId);
                            // Afficher le formulaire
                            editDesignForm.style.display = 'block';
                        }
                    });
                });

                // Initialize the enhanced slider after adding designs
                EnhancedSlider.init();

                // Initialize image zoom functionality
                ImageZoom.init();
            })
            .catch(error => {
                console.error('Erreur lors du chargement des designs:', error);
                const sliderElement = document.querySelector('.slider');
                if (sliderElement) sliderElement.innerHTML = '<div>Erreur lors du chargement des designs</div>';
            });
    },

    // Edit design functionality
    editDesign: function(designId) {
        console.log(`Editing design with ID: ${designId}`);
        // Implement design editing logic here
    },

    getSelectedDesigns: function() {
        const selectedDesigns = [];
        document.querySelectorAll('.chqbox:checked').forEach(checkbox => {
            selectedDesigns.push(checkbox.getAttribute('data-design-id'));
        });
        return selectedDesigns;
    }
};

// Fonction pour mettre à jour le statut ads d'un design
function updateDesignAdsStatus(designId, adsValue) {
    console.log(`Mise à jour du statut ads: ID=${designId}, valeur=${adsValue}`);

    const nicheName = document.querySelector('h2.titro').textContent;
    const categoryName = document.querySelector('h2.categ').textContent;

    // Préparation des données pour l'API
    const data = {
        id: designId,
        niche_name: nicheName,
        category_name: categoryName,
        ads_value: adsValue
    };

    // Appel à l'API pour modifier le statut ads du design
    fetch('/api/update_design_ads', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error("Erreur lors de la modification du statut ads du design");
        }
        return response.json();
    })
    .then(result => {
        console.log("Résultat de la mise à jour:", result);

        // Mise à jour de l'interface immédiate
        const adsElements = document.querySelectorAll('.ads');
        adsElements.forEach(el => {
            const parentDesign = el.closest('.desimg');
            const editBtn = parentDesign?.querySelector('.editbtnn');

            if (editBtn && editBtn.getAttribute('data-design-id') === designId) {
                if (adsValue === 'ads') {
                    el.textContent = 'Ads';
                    el.className = 'ads';
                } else if (adsValue === 'noads') {
                    el.textContent = '';
                    el.className = 'ads white-bg';
                }
                el.setAttribute('data-ads-value', adsValue);
            }
        });

        // Rafraîchir les designs si nécessaire
        if (typeof DesignManager !== 'undefined' && DesignManager.loadDesignsForNiche) {
            const nicheElement = document.querySelector('.niche-item.selected') ||
                              document.querySelector('.niche-item');
            if (nicheElement) {
                const nicheId = nicheElement.getAttribute('data-niche-id');
                DesignManager.loadDesignsForNiche(nicheId, nicheName);
            }
        }

        alert(`Statut Ads mis à jour avec succès (${adsValue})!`);
    })
    .catch(error => {
        console.error("Erreur:", error);
        alert("Erreur lors de la modification du statut ads du design. Veuillez réessayer.");
    });
}

// Gestionnaire d'événements pour les boutons Ads et NoAds
document.addEventListener('DOMContentLoaded', function() {
    // Bouton Ads
    const adsButton = document.getElementById('adss');
    if (adsButton) {
        adsButton.addEventListener('click', function() {
            // Obtenir l'ID du design actuellement affiché
            const editDesignForm = document.getElementById('editdesignfrm');
            if (!editDesignForm) {
                alert("Veuillez d'abord sélectionner un design");
                return;
            }

            const designId = editDesignForm.getAttribute('data-design-id');
            if (!designId) {
                alert("ID de design non trouvé");
                return;
            }

            // Appeler l'API pour mettre à jour le statut "ads"
            updateDesignAdsStatus(designId, 'ads');
        });
    }

    // Bouton NoAds
    const noAdsButton = document.getElementById('noads');
    if (noAdsButton) {
        noAdsButton.addEventListener('click', function() {
            // Obtenir l'ID du design actuellement affiché
            const editDesignForm = document.getElementById('editdesignfrm');
            if (!editDesignForm) {
                alert("Veuillez d'abord sélectionner un design");
                return;
            }

            const designId = editDesignForm.getAttribute('data-design-id');
            if (!designId) {
                alert("ID de design non trouvé");
                return;
            }

            // Appeler l'API pour mettre à jour le statut "noads"
            updateDesignAdsStatus(designId, 'noads');
        });
    }
});
// Enhanced Slider download ***********************************************************************************
// Slider simplifié avec défilement natif
const EnhancedSlider = {
    slider: null,
    slides: [],
    init: function() {
        console.log("Initialisation du slider avec défilement natif...");
        // Récupérer les éléments du DOM
        this.slider = document.querySelector('.slider');
        if (!this.slider) {
            console.error("Élément slider non trouvé");
            return;
        }
        // Récupérer tous les slides
        this.slides = document.querySelectorAll('.desimg');
        if (this.slides.length === 0) {
            console.error("Aucun slide trouvé");
            return;
        }
        console.log(`Nombre de slides détectés: ${this.slides.length}`);
        console.log("Slider avec défilement natif initialisé avec succès");
    }
};

// Fonction pour réinitialiser le slider
function resetSlider() {
    // Cette fonction peut être appelée après avoir chargé de nouvelles images
    EnhancedSlider.init();
}
//************************************************************************************************************************************* */
// Image Zoom Functionality
const ImageZoom = {
    overlay: null,
    fullImg: null,
    closeBtn: null,

    createOverlay: function() {
        this.overlay = DOM.create('div', { id: 'image-overlay' });
        this.overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 1);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        `;
        document.body.appendChild(this.overlay);
        this.fullImg = DOM.create('img', { id: 'fullscreen-img' });
        this.fullImg.style.cssText = `
            max-width: 90%;
            max-height: 90%;
            object-fit: contain;
        `;
        this.overlay.appendChild(this.fullImg);
        this.closeBtn = DOM.create('span');
        this.closeBtn.innerHTML = '&times;';
        this.closeBtn.style.cssText = `
            position: absolute;
            top: 20px;
            right: 30px;
            font-size: 40px;
            color: white;
            cursor: pointer;
        `;
        this.overlay.appendChild(this.closeBtn);
    },
    setupEventListeners: function() {
        // Select all images with class slide
        const images = document.querySelectorAll('.slide');
        console.log("Nombre d'images trouvées:", images.length);
        // Add event listener to each image
        images.forEach((img) => {
            console.log("Configuration de l'image:", img.src);
            // Add pointer style to indicate image is clickable
            img.style.cursor = 'pointer';
            // Add click event
            img.onclick = (e) => {
                e.preventDefault();
                console.log("Image cliquée:", img.src);
                // Show image in fullscreen
                this.fullImg.src = img.src;
                this.overlay.style.display = 'flex';
            };
        });
        // Close overlay on close button click
        this.closeBtn.onclick = () => {
            this.overlay.style.display = 'none';
        };
        // Close overlay on click outside image
        this.overlay.onclick = (e) => {
            if (e.target === this.overlay) {
                this.overlay.style.display = 'none';
            }
        };
    },
    init: function() {
        this.createOverlay();
        this.setupEventListeners();
        console.log("Script d'affichage plein écran initialisé");
    }
};
//***************** designs updates **************************************************************************************** */
function updateDesignDetails() {
    const editDesignForm = document.getElementById('editdesignfrm');
    if (!editDesignForm) return;

    const designId = editDesignForm.getAttribute('data-design-id');
    const newTitle = document.getElementById('edittiteldesign').value.trim();
    const newBrand = document.getElementById('editbranddesign').value.trim();
    const nicheName = document.querySelector('h2.titro').textContent;
    const categoryName = document.querySelector('h2.categ').textContent;

    if (!designId) {
        console.error("ID du design non trouvé");
        return;
    }

    // Vérifier si au moins un champ a été modifié
    if (!newTitle && !newBrand) {
        alert("Veuillez entrer un nouveau titre ou une nouvelle marque");
        return;
    }

    // Préparation des données pour l'API
    const data = {
        id: designId,
        title: newTitle || document.getElementById('ttl').textContent,
        brand: newBrand || document.getElementById('brnd').textContent,
        niche_name: nicheName,
        category_name: categoryName
    };

    // Appel à l'API pour modifier les détails du design
    fetch('/api/update_design_details', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error("Erreur lors de la modification des détails du design");
        }
        return response.json();
    })
    .then(result => {
        // Mise à jour des labels
        if (newTitle) {
            document.getElementById('ttl').textContent = newTitle;
        }
        if (newBrand) {
            document.getElementById('brnd').textContent = newBrand;
        }

        // Réinitialiser les champs du formulaire
        document.getElementById('edittiteldesign').value = '';
        document.getElementById('editbranddesign').value = '';

        // Fermer le formulaire
        editDesignForm.style.display = "none";

        // Rafraîchir les designs si nécessaire
        if (typeof DesignManager !== 'undefined' && DesignManager.loadDesignsForNiche) {
            // Récupérer l'ID de la niche
            const nicheElement = document.querySelector('.niche-item.selected') ||
                                 document.querySelector('.niche-item');
            if (nicheElement) {
                const nicheId = nicheElement.getAttribute('data-niche-id');
                DesignManager.loadDesignsForNiche(nicheId, nicheName);
            }
        }

        alert("Les détails du design ont été modifiés avec succès!");
    })
    .catch(error => {
        console.error("Erreur:", error);
        alert("Erreur lors de la modification des détails du design. Veuillez réessayer.");
    });
}
//*****************update statu design ************************************************************************************************ */
// Fonction pour mettre à jour le statut d'un design
function updateDesignStatus(status) {
    const editDesignForm = document.getElementById('editdesignfrm');
    if (!editDesignForm) return;

    const designId = editDesignForm.getAttribute('data-design-id');
    const nicheName = document.querySelector('h2.titro').textContent;
    const categoryName = document.querySelector('h2.categ').textContent;

    if (!designId) {
        console.error("ID du design non trouvé");
        return;
    }

    // Préparation des données pour l'API
    const data = {
        id: designId,
        status: status,
        niche_name: nicheName,
        category_name: categoryName
    };

    // Appel à l'API pour modifier le statut du design
    fetch('/api/update_design_status', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error("Erreur lors de la modification du statut du design");
        }
        return response.json();
    })
    .then(result => {
        // Mise à jour de l'affichage du statut
        const statusDisplay = document.querySelector('.uplod');
        if (statusDisplay) {
            statusDisplay.textContent = status;

            // Mise à jour des classes CSS si nécessaire
            statusDisplay.className = 'uplod';
            if (status === 'Uploaded') {
                //statusDisplay.classList.add('uploaded');
                statusDisplay.style.cssText = "background-color: red !important; color: white !important;";
            } else {
                //statusDisplay.classList.add('not-uploaded');
                statusDisplay.style.cssText = "background-color: green !important; color: white !important;";
            }
        }
        //************************************************** */
        // Rafraîchir les designs si nécessaire
        if (typeof DesignManager !== 'undefined' && DesignManager.loadDesignsForNiche) {
            // Récupérer l'ID de la niche
            const nicheElement = document.querySelector('.niche-item.selected') ||
                               document.querySelector('.niche-item');
            if (nicheElement) {
                const nicheId = nicheElement.getAttribute('data-niche-id');
                DesignManager.loadDesignsForNiche(nicheId, nicheName);
            }
        }

        console.log(`Statut mis à jour: ${status}`);
    })
    .catch(error => {
        console.error("Erreur:", error);
        alert("Erreur lors de la modification du statut du design. Veuillez réessayer.");
    });
}
function afterApiCall() {
   setTimeout(scanAndStyleUplodElements, 200);
 }
// Ajouter les gestionnaires d'événements une fois que le DOM est chargé
document.addEventListener('DOMContentLoaded', function() {
    // Récupérer les boutons
    const uploadedBtn = document.getElementById('pladi');
    const notUploadedBtn = document.getElementById('pladish');

    // Gestionnaire pour le bouton "Uploaded"
    if (uploadedBtn) {
        uploadedBtn.addEventListener('click', function() {
            updateDesignStatus('Uploaded');
        });
    }

    // Gestionnaire pour le bouton "Not Uploaded"
    if (notUploadedBtn) {
        notUploadedBtn.addEventListener('click', function() {
            updateDesignStatus('Not Uploaded');
        });
    }
});
//**********************fction coloeurs****************************************************************************************** */
function colorerElementsSlider() {
    // Sélectionner le slider par sa classe
    const slider = document.querySelector('.slider');
    // Vérifier si le slider existe
    if (!slider) {
      console.error("Le slider avec la classe 'slider' n'a pas été trouvé");
      return;
    }
    // Sélectionner tous les éléments avec la classe 'uplod' à l'intérieur du slider
    const elements = slider.querySelectorAll('.uplod');
    // Vérifier si des éléments ont été trouvés
    if (elements.length === 0) {
      console.error("Aucun élément avec la classe 'uplod' n'a été trouvé dans le slider");
      return;
    }
    // Parcourir chaque élément
    elements.forEach(element => {
      // Récupérer le texte de l'élément (en supprimant les espaces inutiles)
      const texte = element.textContent.trim();
      // Changer la couleur de fond selon le texte
      if (texte === "Uploaded") {
        element.style.backgroundColor = "#e31010"; // Rouge comme dans votre CSS
        element.classList.remove('uplodx');
        element.classList.add('uplod');
      } else if (texte === "Not Uploaded") {
        element.style.backgroundColor = "#06a22d"; // Vert comme dans votre CSS
        element.classList.remove('uplod');
        element.classList.add('uplodx');
      }
    });
    console.log(`${elements.length} éléments ont été traités`);
  }
  document.addEventListener('DOMContentLoaded', function() {
    // Vérifier si l'application est déjà activée
    function checkActivationStatus() {
      const isActivated = localStorage.getItem('appActivated') === 'true';
      const activationKey = localStorage.getItem('activationKey');

      if (isActivated && activationKey === '4NUB-3C11-HAI0-EX1Z') {
        console.log("Application already activated with valid key");
        return true;
      }
      return false;
    }

    // Exécuter la fonction immédiatement au chargement
    colorerElementsSlider();
    setInterval(colorerElementsSlider, 2000); // Vérifier toutes les 2 secondes

    // Vérifier l'état d'activation
    if (checkActivationStatus()) {
      console.log("Application is activated, all features are available");
    } else {
      console.log("Application is not activated, some features may be restricted");
    }

    // S'assurer que les checkboxes cochées restent visibles
    function makeCheckedCheckboxesVisible() {
      const checkedCheckboxes = document.querySelectorAll('.chqbox:checked');
      checkedCheckboxes.forEach(checkbox => {
        // Rendre la checkbox visible sans modifier sa position
        checkbox.style.opacity = '1';
        checkbox.style.visibility = 'visible';
        checkbox.style.zIndex = '30';

        // Conserver exactement la même position
        checkbox.style.top = '-305px';
        checkbox.style.left = '-128px';

        // S'assurer que le conteneur parent ne cache pas la checkbox
        const hoverControls = checkbox.closest('.hover-controls');
        if (hoverControls) {
          // Créer un style spécifique pour ce conteneur
          hoverControls.setAttribute('data-has-checked', 'true');

          // S'assurer que les autres éléments ne sont pas affectés
          const editBtn = hoverControls.querySelector('.editbtnn');
          const mockupBtn = hoverControls.querySelector('.mockupbtnn');

          // Préserver les positions originales des boutons
          if (editBtn) editBtn.style.position = '';
          if (mockupBtn) mockupBtn.style.position = '';
        }
      });
    }

    // Exécuter immédiatement et périodiquement
    makeCheckedCheckboxesVisible();
    setInterval(makeCheckedCheckboxesVisible, 1000);

    // Ajouter des écouteurs d'événements pour les checkboxes
    document.addEventListener('change', function(e) {
      if (e.target && e.target.classList.contains('chqbox')) {
        makeCheckedCheckboxesVisible();
      }
    });

    // Fonction pour vérifier si l'application est activée
    function isAppActivated() {
      const isActivated = localStorage.getItem('appActivated') === 'true';
      const activationKey = localStorage.getItem('activationKey');

      return isActivated && activationKey === '4NUB-3C11-HAI0-EX1Z';
    }

    // Fonction pour afficher la fenêtre d'activation
    function showActivationWindow() {
      // Toujours afficher la fenêtre sarot pour les tests, même si l'application est déjà activée
      const sarotElement = document.getElementById('sarot');
      const overlayElement = document.getElementById('overlay');

      if (sarotElement && overlayElement) {
        // Afficher l'overlay
        overlayElement.style.display = 'block';

        // Afficher l'élément sarot
        sarotElement.style.display = 'flex';

        // Ajouter une animation de fondu
        sarotElement.style.animation = 'fadeIn 0.5s ease forwards';
      }
    }

    // Fonction pour fermer la fenêtre d'activation (uniquement après validation d'une clé)
    function hideActivationWindow() {
      const sarotElement = document.getElementById('sarot');
      const overlayElement = document.getElementById('overlay');

      if (sarotElement && overlayElement) {
        // Masquer l'élément sarot et l'overlay
        sarotElement.style.display = 'none';
        overlayElement.style.display = 'none';
      }
    }

    // Ajouter un gestionnaire d'événements pour le clic sur le logo
    const logoImg = document.getElementById('logo-img');
    if (logoImg) {
      logoImg.addEventListener('click', showActivationWindow);
    }

    // Ajouter un gestionnaire d'événements pour le bouton d'achat de clé
    const activationBtn = document.getElementById('activation-btn');
    if (activationBtn) {
      activationBtn.addEventListener('click', function() {
        // URL d'activation
        const activationUrl = 'https://mbf1972.github.io/merchdays-site/activation.html';

        // Méthode 1: window.open
        try {
          console.log("Trying to open URL with window.open");
          const newWindow = window.open(activationUrl, '_blank');

          // Si window.open est bloqué ou ne fonctionne pas
          if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
            throw new Error("window.open failed");
          }
        } catch (e) {
          console.log("window.open failed, trying alternative methods");

          // Méthode 2: créer un lien et cliquer dessus
          try {
            console.log("Trying to open URL with link click");
            const link = document.createElement('a');
            link.href = activationUrl;
            link.target = '_blank';
            link.rel = 'noopener noreferrer';
            link.textContent = 'Open activation page';
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            setTimeout(() => document.body.removeChild(link), 100);
          } catch (e2) {
            console.log("Link click failed, trying location.href");

            // Méthode 3: redirection directe
            try {
              // Créer un événement personnalisé pour signaler l'intention d'ouvrir un lien externe
              const event = new CustomEvent('openExternalLink', {
                detail: { url: activationUrl }
              });
              document.dispatchEvent(event);
              console.log("Dispatched openExternalLink event");

              // Afficher un message à l'utilisateur
              alert("Si la page ne s'ouvre pas automatiquement, veuillez copier cette URL et l'ouvrir manuellement: " + activationUrl);
            } catch (e3) {
              console.log("All methods failed");
              alert("Impossible d'ouvrir la page d'activation. Veuillez visiter manuellement: " + activationUrl);
            }
          }
        }
      });
    }

    // Fonction pour activer toutes les fonctionnalités
    function activateAllFeatures() {
      console.log("Activating all features...");

      // Stocker l'état d'activation dans le localStorage
      localStorage.setItem('appActivated', 'true');
      localStorage.setItem('activationKey', '4NUB-3C11-HAI0-EX1Z');
      localStorage.setItem('activationDate', new Date().toISOString());

      // Vous pouvez ajouter ici d'autres actions pour activer des fonctionnalités spécifiques
      // Par exemple, activer des éléments d'interface, débloquer des fonctionnalités, etc.

      console.log("All features activated successfully!");
    }

    // Fonction pour valider la clé d'activation
    function validateActivationKey() {
      // Récupérer la valeur du champ de saisie
      const searchInput = document.getElementById('search');
      if (searchInput) {
        const keyValue = searchInput.value.trim();

        // Vérifier si une clé a été saisie
        if (keyValue === '') {
          alert('Veuillez entrer une clé d\'activation.');
          return;
        }

        // Vérifier si la clé est valide (4NUB-3C11-HAI0-EX1Z)
        if (keyValue === '4NUB-3C11-HAI0-EX1Z') {
          // Clé valide
          alert('Clé d\'activation valide ! Toutes les fonctionnalités sont maintenant débloquées.');

          // Activer toutes les fonctionnalités
          activateAllFeatures();

          // Réinitialiser le champ après validation
          searchInput.value = '';

          // Fermer la fenêtre d'activation
          hideActivationWindow();
        } else {
          // Clé invalide
          alert('Clé d\'activation invalide. Veuillez réessayer ou acheter une clé valide.');

          // Réinitialiser le champ pour une nouvelle tentative
          searchInput.value = '';
          searchInput.focus();
        }
      }
    }

    // Ajouter un gestionnaire d'événements pour le bouton "Activate"
    const validateBtn = document.getElementById('valid');
    if (validateBtn) {
      validateBtn.addEventListener('click', validateActivationKey);
    }

    // Ajouter un gestionnaire d'événements pour la touche Entrée dans le champ de saisie
    const searchInput = document.getElementById('search');
    if (searchInput) {
      searchInput.addEventListener('keypress', function(event) {
        // Vérifier si la touche pressée est Entrée (code 13)
        if (event.key === 'Enter') {
          // Empêcher le comportement par défaut (soumission de formulaire)
          event.preventDefault();
          // Valider la clé
          validateActivationKey();
        }
      });
    }
  });
//**************supprimer design ************************************************************************************************ */
function deleteDesign() {
    const editDesignForm = document.getElementById('editdesignfrm');
    if (!editDesignForm) return;

    const designId = editDesignForm.getAttribute('data-design-id');
    const nicheName = document.querySelector('h2.titro').textContent;
    const categoryName = document.querySelector('h2.categ').textContent;

    if (!designId) {
        console.error("ID du design non trouvé");
        return;
    }

    // Demander confirmation avant de supprimer
    if (!confirm("Êtes-vous sûr de vouloir supprimer ce design ? Cette action est irréversible.")) {
        return; // L'utilisateur a annulé
    }

    // Préparation des données pour l'API
    const data = {
        id: designId,
        niche_name: nicheName,
        category_name: categoryName
    };

    // Appel à l'API pour supprimer le design
    fetch('/api/delete_design', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error("Erreur lors de la suppression du design");
        }
        return response.json();
    })
    .then(result => {
        // Fermer le formulaire
        editDesignForm.style.display = "none";

        // Rafraîchir les designs pour cette niche
        if (typeof DesignManager !== 'undefined' && DesignManager.loadDesignsForNiche) {
            // Récupérer l'ID de la niche
            const nicheElement = document.querySelector('.niche-item.selected') ||
                                document.querySelector('.niche-item');
            if (nicheElement) {
                const nicheId = nicheElement.getAttribute('data-niche-id');
                DesignManager.loadDesignsForNiche(nicheId, nicheName);
            }
        } else {
            // Reload si DesignManager n'est pas disponible
            location.reload();
        }

        alert("Le design a été supprimé avec succès!");
    })
    .catch(error => {
        console.error("Erreur:", error);
        alert("Erreur lors de la suppression du design. Veuillez réessayer.");
    });
}
//***********move design to archive *********************************************************************************************** */
function moveDesignToArchive() {
    const editDesignForm = document.getElementById('editdesignfrm');
    if (!editDesignForm) return;

    const designId = editDesignForm.getAttribute('data-design-id');
    const nicheName = document.querySelector('h2.titro').textContent;
    const categoryName = document.querySelector('h2.categ').textContent;

    if (!designId) {
        console.error("ID du design non trouvé");
        return;
    }

    // Demander confirmation avant d'archiver
    if (!confirm("Êtes-vous sûr de vouloir déplacer ce design vers les archives ?")) {
        return; // L'utilisateur a annulé
    }

    // Préparation des données pour l'API
    const data = {
        id: designId,
        niche_name: nicheName,
        category_name: categoryName
    };

    // Appel à l'API pour archiver le design
    fetch('/api/archive_design', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error("Erreur lors de l'archivage du design");
        }
        return response.json();
    })
    .then(result => {
        // Fermer le formulaire
        editDesignForm.style.display = "none";

        // Rafraîchir les designs pour cette niche
        if (typeof DesignManager !== 'undefined' && DesignManager.loadDesignsForNiche) {
            // Récupérer l'ID de la niche
            const nicheElement = document.querySelector('.niche-item.selected') ||
                                document.querySelector('.niche-item');
            if (nicheElement) {
                const nicheId = nicheElement.getAttribute('data-niche-id');
                DesignManager.loadDesignsForNiche(nicheId, nicheName);
            }
        } else {
            // Reload si DesignManager n'est pas disponible
            location.reload();
        }

        alert("Le design a été déplacé vers les archives avec succès!");
    })
    .catch(error => {
        console.error("Erreur:", error);
        alert("Erreur lors de l'archivage du design. Veuillez réessayer.");
    });
}
//******************************************************************************************************************************** */
// Niche Form Management**********************************************************************************************
const NicheFormManager = {
    addNicheButton: null,
    addNicheForm: null,
    closeFormButton: null,
    saveNicheButton: null,
    nicheNameInput: null,
    nicheCategorySelect: null,
    openNicheForm: function() {
        // Reset form
        this.nicheNameInput.value = '';
        this.nicheCategorySelect.value = '';
        // Show form
        this.addNicheForm.style.display = 'block';
    },
    closeNicheForm: function() {
        this.addNicheForm.style.display = 'none';
    },
    saveNiche: function() {
        const nicheName = this.nicheNameInput.value.trim();
        const categoryId = this.nicheCategorySelect.value;
        // Simple validation
        if (!nicheName) {
            alert('Please enter a niche name');
            return;
        }
        if (!categoryId) {
            alert('Please select a category');
            return;
        }
        // Prepare data to send
        const nicheData = {
            name: nicheName,
            category_id: categoryId
        };
        // Send data to server
        fetch('/api/niches', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(nicheData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            alert('Niche added successfully!');
            this.closeNicheForm();
        })
        .catch(error => {
            console.error('Error adding niche:', error);
            alert('Error adding niche. Please try again.');
        });
    },
    loadCategories: function() {
        fetch('/api/categories')
            .then(response => response.json())
            .then(categories => {
                // Keep default option
                const defaultOption = this.nicheCategorySelect.querySelector('option[value=""]');
                // Clear existing options
                this.nicheCategorySelect.innerHTML = '';
                this.nicheCategorySelect.appendChild(defaultOption);
                // Add categories from database
                categories.forEach(category => {
                    const option = DOM.create('option', { value: category.id });
                    option.textContent = category.name;
                    this.nicheCategorySelect.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Error loading categories:', error);
            });
    },

    init: function() {
        this.addNicheButton = DOM.getById('addniche');
        this.addNicheForm = DOM.getById('addnichfrm');
        this.closeFormButton = DOM.getById('close-form');
        this.saveNicheButton = DOM.getById('save-niche');
        this.nicheNameInput = DOM.getById('niche-name');
        this.nicheCategorySelect = DOM.getById('nicheCategory');
        if (!this.addNicheButton || !this.addNicheForm || !this.closeFormButton ||
            !this.saveNicheButton || !this.nicheNameInput || !this.nicheCategorySelect) {
            console.warn('Some niche form elements not found');
            return;
        }
        // Add event listeners
        this.addNicheButton.addEventListener('click', () => this.openNicheForm());
        this.closeFormButton.addEventListener('click', () => this.closeNicheForm());
        this.saveNicheButton.addEventListener('click', () => this.saveNiche());
        // Load categories
        this.loadCategories();
        // Close form by default
        this.addNicheForm.style.display = 'none';
    }
};
//***************categorie / niches upload design****************************************************************************** */
//***************categorie / niches upload design****************************************************************************** */
// Fonction pour charger les catégories dans le select
function loadCategories() {
    const categorySelect = document.getElementById('nicheCategorys');
    // Vider le select sauf l'option par défaut
    while (categorySelect.options.length > 1) {
        categorySelect.remove(1);
    }
    // Appel à l'API pour récupérer les catégories
    fetch('/api/categories')
        .then(response => response.json())
        .then(categories => {
            // Ajouter chaque catégorie au select
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                categorySelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Erreur lors du chargement des catégories:', error);
        });
}
// Fonction pour charger les niches d'une catégorie
function loadNichesByCategory(categoryName) {
    const nicheSelect = document.getElementById('nicheName');
    // Vider le select sauf l'option par défaut
    while (nicheSelect.options.length > 1) {
        nicheSelect.remove(1);
    }
    // Si aucune catégorie n'est sélectionnée, ne rien faire
    if (!categoryName) {
        return;
    }
    // Appel à l'API pour récupérer les niches de la catégorie
    fetch(`/api/niches_by_category/${categoryName}`)
        .then(response => response.json())
        .then(niches => {
            // Si aucune niche trouvée
            if (niches.length === 0) {
                const option = document.createElement('option');
                option.value = "";
                option.textContent = "Aucune niche trouvée";
                option.disabled = true;
                nicheSelect.appendChild(option);
                return;
            }

            // Ajouter chaque niche au select
            niches.forEach(niche => {
                const option = document.createElement('option');
                option.value = niche.id;
                option.textContent = niche.name;
                nicheSelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Erreur lors du chargement des niches:', error);
        });
}
//********************upload image************************************************************************************** */
// Variables pour stocker les fichiers sélectionnés
let selectedFile = null;        // Pour l'upload simple
let selectedFiles = [];         // Pour l'upload multiple
let isUploading = false;        // Flag pour éviter les uploads simultanés
const UPLOAD_DELAY = 1500;      // Délai entre chaque upload (millisecondes)
const MAX_RETRIES = 3;          // Nombre maximum de tentatives par fichier
document.addEventListener('DOMContentLoaded', function() {
    // Initialiser les sélecteurs de catégories et niches au chargement du formulaire
    const uploadDesForm = document.getElementById('uploaddesfrm');
    const categorySelect = document.getElementById('nicheCategorys');
    // Si le formulaire est présent
    if (uploadDesForm) {
        // Charger les catégories
        loadCategories();
        // Ajouter l'événement de changement de catégorie
        categorySelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const categoryName = selectedOption.textContent;
            loadNichesByCategory(categoryName);
        });
    }
    // Configuration pour l'upload de designs
    //const uploadButton = document.getElementById('uploadfrompc');
    const uploadMultipleButton = document.getElementById('uploadfrompcall');
    const saveButton = document.getElementById('savechangee');
    // Configuration pour l'upload multiple && imagePreview
    if (uploadMultipleButton) {
        // Créer un input de type file invisible pour upload multiple
        const multipleFileInput = document.createElement('input');
        multipleFileInput.type = 'file';
        multipleFileInput.accept = 'image/*';  // Accepter uniquement les images
        multipleFileInput.multiple = true;     // Permettre la sélection multiple
        multipleFileInput.style.display = 'none';
        document.body.appendChild(multipleFileInput);
        // Déclencher le sélecteur de fichiers multiples quand on clique sur le bouton
        uploadMultipleButton.addEventListener('click', function() {
            // Réinitialiser selectedFile pour indiquer qu'on est en mode upload multiple
            selectedFile = null;
            multipleFileInput.click();
        });
        // Quand plusieurs fichiers sont sélectionnés
        multipleFileInput.addEventListener('change', function() {
            if (this.files && this.files.length > 0) {
                // Stocker les fichiers multiples
                selectedFiles = Array.from(this.files);
                const reader = new FileReader();

                reader.onload = function(e) {
                    // Afficher l'image dans l'élément img
                    imagePreview.src = e.target.result;
                };

                reader.readAsDataURL(selectedFiles[0]);
                alert(`${selectedFiles.length} fichiers sélectionnés pour upload`);
            }
        });
    }
    // Gestionnaire pour le bouton "Save"
    if (saveButton) {
        saveButton.addEventListener('click', function() {
            // Éviter les uploads multiples simultanés
            if (isUploading) {
                alert("Un upload est déjà en cours, veuillez patienter...");
                return;
            }
            // Déterminer quel type d'upload effectuer
            if (selectedFiles.length > 0) {
                saveMultipleDesigns();
            } else if (selectedFile) {
                saveDesign();
            } else {
                alert("Veuillez sélectionner au moins une image");
            }
        });
    }
});
function saveDesign() {
    const categorySelect = document.getElementById('nicheCategorys');
    const nicheSelect = document.getElementById('nicheName');
    const titleInput = document.getElementById('titeldesign');
    const brandInput = document.getElementById('branddesign');
    if (!selectedFile) {
        alert("Veuillez sélectionner une image");
        return;
    }
    if (!categorySelect.value) {
        alert("Veuillez sélectionner une catégorie");
        return;
    }
    if (!nicheSelect.value) {
        alert("Veuillez sélectionner une niche");
        return;
    }
    const title = titleInput.value.trim();
    if (!title) {
        alert("Veuillez entrer un titre pour le design");
        return;
    }
    const brand = brandInput.value.trim();
    if (!brand) {
        alert("Veuillez entrer une marque pour le design");
        return;
    }
    // Indiquer que l'upload est en cours
    isUploading = true
    // Afficher un message de chargement
    const loadingMessage = showLoading("Upload en cours...");
    const formData = new FormData();
    formData.append('file', selectedFile);
    formData.append('niche_id', nicheSelect.value);
    formData.append('title', title);
    formData.append('brand', brand);
    // Envoyer la requête
    fetch('/api/upload_design', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error("Erreur lors de l'enregistrement du design");
        }
        return response.json();
    })
    .then(result => {
        // Cacher le message de chargement
        hideLoading(loadingMessage);
        //imageElement.src = "static/logo.png";
        titleInput.value = "";
        brandInput.value = "";
        selectedFile = null;
        // Indiquer que l'upload est terminé
        isUploading = false;
        // Afficher un message de succès
        alert("Design enregistré avec succès!");
        // Fermer le formulaire
        document.getElementById('uploaddesfrm').style.display = "none";
    })
    .catch(error => {
        console.error("Erreur:", error);
        // Cacher le message de chargement
        hideLoading(loadingMessage);
        alert("Erreur lors de l'enregistrement du design. Veuillez réessayer.");
        // Indiquer que l'upload est terminé même en cas d'erreur
        isUploading = false;
    });
}
// Fonction helper pour afficher un loader
function showLoading(message) {
    const loadingDiv = document.createElement('div');
    loadingDiv.style.position = 'fixed';
    loadingDiv.style.top = '50%';
    loadingDiv.style.left = '50%';
    loadingDiv.style.transform = 'translate(-50%, -50%)';
    loadingDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
    loadingDiv.style.color = 'white';
    loadingDiv.style.padding = '20px';
    loadingDiv.style.borderRadius = '5px';
    loadingDiv.style.zIndex = '9999';
    loadingDiv.style.textAlign = 'center';
    loadingDiv.textContent = message || "Chargement...";

    document.body.appendChild(loadingDiv);
    return loadingDiv;
}
// Fonction helper pour cacher un loader
function hideLoading(loadingElement) {
    if (loadingElement && loadingElement.parentNode) {
        loadingElement.parentNode.removeChild(loadingElement);
    }
}
// Fonction pour upload un fichier avec plusieurs tentatives en cas d'échec
async function uploadSingleFile(file, nicheId, title, brand, retryCount = 0) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('niche_id', nicheId);
    formData.append('title', title);
    formData.append('brand', brand);
    try {
        // Attendre que la requête soit complétée avant de continuer
        const response = await fetch('/api/upload_design', {
            method: 'POST',
            body: formData
        });
        // Si la requête a échoué avec un code d'erreur
        if (!response.ok) {
            if (retryCount < MAX_RETRIES) {
                console.log(`Tentative ${retryCount + 1} échouée pour ${file.name}, nouvelle tentative dans ${UPLOAD_DELAY}ms...`);
                // Attendre un certain temps avant de réessayer
                await new Promise(resolve => setTimeout(resolve, UPLOAD_DELAY));
                // Appel récursif avec compteur de tentative incrémenté
                return await uploadSingleFile(file, nicheId, title, brand, retryCount + 1);
            } else {
                throw new Error(`Échec après ${MAX_RETRIES} tentatives (HTTP ${response.status})`);
            }
        }
        const result = await response.json();
        console.log("Upload réussi:", result);
        return { success: true, result };
    } catch (error) {
        // Si nous n'avons pas dépassé le nombre maximum de tentatives, réessayer
        if (retryCount < MAX_RETRIES) {
            console.log(`Erreur d'upload (${error.message}), tentative ${retryCount + 1}/${MAX_RETRIES}, nouvelle tentative dans ${UPLOAD_DELAY}ms...`);
            // Attendre un certain temps avant de réessayer
            await new Promise(resolve => setTimeout(resolve, UPLOAD_DELAY));
            // Appel récursif avec compteur de tentative incrémenté
            return await uploadSingleFile(file, nicheId, title, brand, retryCount + 1);
        } else {
            console.error(`Échec définitif après ${MAX_RETRIES} tentatives:`, error);
            return { success: false, error: error.message };
        }
    }
}
// Créer un élément de progression avancé avec capacité d'annulation
function createProgressUI() {
    // Conteneur principal
    const progressContainer = document.createElement('div');
    progressContainer.style.position = 'fixed';
    progressContainer.style.top = '50%';
    progressContainer.style.left = '50%';
    progressContainer.style.transform = 'translate(-50%, -50%)';
    progressContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
    progressContainer.style.color = 'white';
    progressContainer.style.padding = '20px';
    progressContainer.style.borderRadius = '10px';
    progressContainer.style.zIndex = '9999';
    progressContainer.style.width = '80%';
    progressContainer.style.maxWidth = '500px';
    progressContainer.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.5)';
    // Titre
    const titleElement = document.createElement('h3');
    titleElement.textContent = 'Upload Multiple en cours';
    titleElement.style.margin = '0 0 15px 0';
    titleElement.style.textAlign = 'center';
    progressContainer.appendChild(titleElement);
    // Texte principal de progression
    const progressText = document.createElement('div');
    progressText.textContent = 'Préparation...';
    progressText.style.marginBottom = '10px';
    progressText.style.textAlign = 'center';
    progressText.style.fontWeight = 'bold';
    progressContainer.appendChild(progressText);
    // Barre de progression
    const progressBarContainer = document.createElement('div');
    progressBarContainer.style.width = '100%';
    progressBarContainer.style.height = '20px';
    progressBarContainer.style.backgroundColor = '#333';
    progressBarContainer.style.borderRadius = '10px';
    progressBarContainer.style.overflow = 'hidden';
    progressBarContainer.style.marginBottom = '15px';
    const progressFill = document.createElement('div');
    progressFill.style.width = '0%';
    progressFill.style.height = '100%';
    progressFill.style.backgroundColor = '#4CAF50';
    progressFill.style.transition = 'width 0.3s';
    progressBarContainer.appendChild(progressFill);
    progressContainer.appendChild(progressBarContainer);
    // Zone de détails
    const detailsContainer = document.createElement('div');
    detailsContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.3)';
    detailsContainer.style.padding = '10px';
    detailsContainer.style.borderRadius = '5px';
    detailsContainer.style.marginBottom = '15px';
    detailsContainer.style.maxHeight = '100px';
    detailsContainer.style.overflowY = 'auto';
    // Détails du fichier en cours
    const currentFileDetails = document.createElement('div');
    currentFileDetails.textContent = 'En attente...';
    currentFileDetails.style.marginBottom = '5px';
    detailsContainer.appendChild(currentFileDetails);
    // Statistiques d'upload
    const statsElement = document.createElement('div');
    statsElement.style.display = 'flex';
    statsElement.style.justifyContent = 'space-between';
    statsElement.style.marginBottom = '5px';
    const successStats = document.createElement('span');
    successStats.textContent = 'Réussis: 0';
    successStats.style.color = '#4CAF50';
    const failedStats = document.createElement('span');
    failedStats.textContent = 'Échecs: 0';
    failedStats.style.color = '#f44336';
    const pendingStats = document.createElement('span');
    pendingStats.textContent = 'En attente: 0';
    statsElement.appendChild(successStats);
    statsElement.appendChild(failedStats);
    statsElement.appendChild(pendingStats);
    detailsContainer.appendChild(statsElement);
    // Journal des activités
    const logElement = document.createElement('div');
    logElement.style.fontSize = '12px';
    logElement.style.color = '#ccc';
    logElement.style.marginTop = '10px';
    logElement.style.maxHeight = '60px';
    logElement.style.overflowY = 'auto';
    detailsContainer.appendChild(logElement);
    progressContainer.appendChild(detailsContainer);
    // Bouton d'annulation
    const cancelButton = document.createElement('button');
    cancelButton.textContent = 'Annuler l\'upload';
    cancelButton.style.padding = '8px 16px';
    cancelButton.style.backgroundColor = '#f44336';
    cancelButton.style.color = 'white';
    cancelButton.style.border = 'none';
    cancelButton.style.borderRadius = '4px';
    cancelButton.style.cursor = 'pointer';
    cancelButton.style.display = 'block';
    cancelButton.style.margin = '0 auto';
    progressContainer.appendChild(cancelButton);
    // Ajouter l'élément au document
    document.body.appendChild(progressContainer);
    // Fonctions de mise à jour
    const updateProgress = (current, total) => {
        const percentage = Math.round((current / total) * 100);
        progressFill.style.width = `${percentage}%`;
        progressText.textContent = `Progression: ${current}/${total} (${percentage}%)`;
    };
    const updateCurrentFile = (fileName, attempt = 1) => {
        if (attempt > 1) {
            currentFileDetails.textContent = `${fileName} (tentative ${attempt}/${MAX_RETRIES})`;
        } else {
            currentFileDetails.textContent = fileName;
        }
    };
    const updateStats = (success, failed, pending) => {
        successStats.textContent = `Réussis: ${success}`;
        failedStats.textContent = `Échecs: ${failed}`;
        pendingStats.textContent = `En attente: ${pending}`;
    };
    const addLogEntry = (message, isError = false) => {
        const entry = document.createElement('div');
        entry.textContent = message;
        if (isError) {
            entry.style.color = '#f44336';
        }
        logElement.appendChild(entry);
        // Faire défiler vers le bas pour voir la dernière entrée
        logElement.scrollTop = logElement.scrollHeight;
    };
    const close = () => {
        if (progressContainer.parentNode) {
            document.body.removeChild(progressContainer);
        }
    };
    // Retourner les fonctions et éléments utiles
    return {
        element: progressContainer,
        updateProgress,
        updateCurrentFile,
        updateStats,
        addLogEntry,
        close,
        cancelButton
    };
}
// Fonction améliorée pour sauvegarder plusieurs designs
async function saveMultipleDesigns() {
    const categorySelect = document.getElementById('nicheCategorys');
    const nicheSelect = document.getElementById('nicheName');
    const titleInput = document.getElementById('titeldesign');
    const brandInput = document.getElementById('branddesign');
    if (selectedFiles.length === 0) {
        alert("Veuillez sélectionner des images");
        return;
    }

    if (!categorySelect.value) {
        alert("Veuillez sélectionner une catégorie");
        return;
    }

    if (!nicheSelect.value) {
        alert("Veuillez sélectionner une niche");
        return;
    }
    const title = titleInput.value.trim();
    if (!title) {
        alert("Veuillez entrer un titre pour les designs");
        return;
    }
    const brand = brandInput.value.trim();
    if (!brand) {
        alert("Veuillez entrer une marque pour les designs");
        return;
    }
    // Afficher un message de confirmation avec le nombre de fichiers
    if (!confirm(`Vous allez uploader ${selectedFiles.length} designs avec les mêmes informations. Continuer?`)) {
        return; // L'utilisateur a annulé
    }
    // Indiquer que l'upload est en cours
    isUploading = true;
    // Créer l'interface de progression
    const progressUI = createProgressUI();
    // Variables de suivi
    const totalFiles = selectedFiles.length;
    let uploadedCount = 0;
    let failedCount = 0;
    let isCancelled = false;
    // Gérer le clic sur le bouton d'annulation
    progressUI.cancelButton.addEventListener('click', () => {
        if (confirm('Êtes-vous sûr de vouloir annuler l\'upload?')) {
            isCancelled = true;
            progressUI.addLogEntry('Annulation en cours... Attente de la fin de l\'upload actuel.');
        }
    });
    // Mettre à jour les statistiques initiales
    progressUI.updateStats(0, 0, totalFiles);
    progressUI.updateProgress(0, totalFiles);
    try {
        // Copier le tableau des fichiers pour pouvoir le manipuler
        const filesToUpload = [...selectedFiles];
        // Upload des fichiers un par un
        for (let i = 0; i < totalFiles && !isCancelled; i++) {
            const currentFile = filesToUpload[i];
            // Mettre à jour l'interface de progression
            progressUI.updateProgress(i, totalFiles);
            progressUI.updateCurrentFile(currentFile.name);
            progressUI.addLogEntry(`Début de l'upload: ${currentFile.name}`);
            let attempt = 1;
            let success = false;
            // Boucle de tentatives avec délais de plus en plus longs
            while (attempt <= MAX_RETRIES && !success && !isCancelled) {
                // Mettre à jour l'information de tentative
                if (attempt > 1) {
                    progressUI.updateCurrentFile(currentFile.name, attempt);
                    progressUI.addLogEntry(`Nouvelle tentative (${attempt}/${MAX_RETRIES}): ${currentFile.name}`);
                }

                try {
                    // Uploader le fichier
                    await new Promise(resolve => setTimeout(resolve, UPLOAD_DELAY));

                    const result = await fetch('/api/upload_design', {
                        method: 'POST',
                        body: (() => {
                            const formData = new FormData();
                            formData.append('file', currentFile);
                            formData.append('niche_id', nicheSelect.value);
                            formData.append('title', title);
                            formData.append('brand', brand);
                            return formData;
                        })()
                    });

                    // Vérifier si la requête a réussi
                    if (result.ok) {
                        const data = await result.json();
                        if (data.success) {
                            success = true;
                            uploadedCount++;
                            progressUI.addLogEntry(`✓ Succès: ${currentFile.name}`);
                            break; // Sortir de la boucle de tentatives
                        } else {
                            throw new Error(data.error || 'Erreur inconnue');
                        }
                    } else {
                        throw new Error(`Erreur HTTP: ${result.status}`);
                    }
                } catch (error) {
                    if (attempt >= MAX_RETRIES) {
                        progressUI.addLogEntry(`✗ Échec définitif: ${currentFile.name} (${error.message})`, true);
                        failedCount++;
                    } else {
                        progressUI.addLogEntry(`Échec: ${currentFile.name} (${error.message})`, true);
                        // Augmenter le délai à chaque tentative
                        await new Promise(resolve => setTimeout(resolve, UPLOAD_DELAY * attempt));
                    }
                }

                attempt++;
            }
            // Si l'upload a été annulé pendant une tentative
            if (isCancelled) {
                progressUI.addLogEntry('Upload annulé par l\'utilisateur');
                break;
            }
            // Mettre à jour les statistiques
            progressUI.updateStats(uploadedCount, failedCount, totalFiles - i - 1);
        }
        // Mise à jour finale de la progression
        progressUI.updateProgress(isCancelled ? uploadedCount + failedCount : totalFiles, totalFiles);
        let finalMessage;
        if (isCancelled) {
            finalMessage = `Upload annulé: ${uploadedCount} fichier(s) réussi(s), ${failedCount} échec(s), ${totalFiles - uploadedCount - failedCount} non traité(s)`;
        } else if (failedCount === 0) {
            finalMessage = `Upload terminé avec succès! Tous les ${totalFiles} fichiers ont été uploadés.`;
        } else {
            finalMessage = `Upload terminé: ${uploadedCount} fichier(s) réussi(s), ${failedCount} échec(s)`;
        }
        progressUI.addLogEntry(finalMessage);
        // Attendre un peu avant de fermer l'interface
        await new Promise(resolve => setTimeout(resolve, 2000));
        titleInput.value = "";
        brandInput.value = "";
        selectedFiles = [];
        // Afficher un message récapitulatif
        alert(finalMessage);
        // Fermer le formulaire si tous les fichiers ont été uploadés avec succès
        if (!isCancelled && failedCount === 0) {
            document.getElementById('uploaddesfrm').style.display = "none";
        }
    } catch (error) {
        console.error("Erreur générale:", error);
        progressUI.addLogEntry(`Erreur système: ${error.message}`, true);
        alert("Une erreur système est survenue pendant l'upload: " + error.message);
    } finally {
        // S'assurer de toujours nettoyer
        progressUI.close();
        isUploading = false;
    }
}
//**************************************************************************************************************************************/
//***************ads************************************************************************************************ */
document.addEventListener('DOMContentLoaded', function() {
    // Bouton Ads
    const adsButton = document.getElementById('adss');
    if (adsButton) {
        adsButton.addEventListener('click', function() {
            // Obtenir l'ID du design actuellement affiché
            const editDesignForm = document.getElementById('editdesignfrm');
            if (!editDesignForm) {
                alert("Veuillez d'abord sélectionner un design");
                return;
            }

            const designId = editDesignForm.getAttribute('data-design-id');
            if (!designId) {
                alert("ID de design non trouvé");
                return;
            }

            // Appeler l'API pour mettre à jour le statut "ads"
            updateDesignAdsStatus(designId, 'ads');
        });
    }

    // Bouton NoAds
    const noAdsButton = document.getElementById('noads');
    if (noAdsButton) {
        noAdsButton.addEventListener('click', function() {
            // Obtenir l'ID du design actuellement affiché
            const editDesignForm = document.getElementById('editdesignfrm');
            if (!editDesignForm) {
                alert("Veuillez d'abord sélectionner un design");
                return;
            }

            const designId = editDesignForm.getAttribute('data-design-id');
            if (!designId) {
                alert("ID de design non trouvé");
                return;
            }

            // Appeler l'API pour mettre à jour le statut "noads"
            updateDesignAdsStatus(designId, 'noads');
        });
    }
});

// Fonction pour mettre à jour le statut ads d'un design
function updateDesignAdsStatus(designId, adsValue) {
    const nicheName = document.querySelector('h2.titro').textContent;
    const categoryName = document.querySelector('h2.categ').textContent;

    // Préparation des données pour l'API
    const data = {
        id: designId,
        niche_name: nicheName,
        category_name: categoryName,
        ads_value: adsValue  // Inclure la valeur à définir
    };

    // Appel à l'API pour modifier le statut ads du design
    fetch('/api/update_design_ads', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error("Erreur lors de la modification du statut ads du design");
        }
        return response.json();
    })
    .then(result => {
        // Mise à jour de l'affichage du statut ads si nécessaire
        const adsDisplay = document.querySelector('.ads');
        if (adsDisplay) {
            adsDisplay.textContent = adsValue;
        }

        // Rafraîchir les designs si nécessaire
        if (typeof DesignManager !== 'undefined' && DesignManager.loadDesignsForNiche) {
            // Récupérer l'ID de la niche
            const nicheElement = document.querySelector('.niche-item.selected') ||
                                document.querySelector('.niche-item');
            if (nicheElement) {
                const nicheId = nicheElement.getAttribute('data-niche-id');
                DesignManager.loadDesignsForNiche(nicheId, nicheName);
            }
        }

        //alert(`Statut Ads mis à jour avec succès (${adsValue})!`);
    })
    .catch(error => {
        console.error("Erreur:", error);
        //alert("Erreur lors de la modification du statut ads du design. Veuillez réessayer.");
    });
}
//********filtration upload not upload***************************************************************************************************/
// Récupération des boutons
document.addEventListener('DOMContentLoaded', function() {
    const boutonUploaded = document.getElementById('upld');
    const boutonNotUploaded = document.getElementById('notup');

    if (boutonUploaded && boutonNotUploaded) {
      // Fonction pour filtrer les éléments selon le statut d'upload
      function filtrerParStatutUpload(status) {
        // Récupérer le slider
        const slider = document.querySelector('.slider');
        if (!slider) return;

        // Récupérer tous les éléments de design dans le slider
        const elementsDesign = slider.querySelectorAll('.desimg');

        console.log(`Filtrage par statut: ${status}, éléments trouvés: ${elementsDesign.length}`);

        // Parcourir chaque élément de design
        elementsDesign.forEach(element => {
          // Trouver l'élément qui contient le statut d'upload
          // Peut être .uplod ou .uplodx selon les cas
          const statutElement = element.querySelector('.uplod, .uplodx');

          if (!statutElement) {
            console.log('Élément de statut non trouvé');
            return;
          }

          // Récupérer le texte du statut
          const statutTexte = statutElement.textContent.trim();
          console.log(`Statut trouvé: "${statutTexte}"`);

          // Vérifier si c'est un élément Uploaded ou Not Uploaded
          if (status === 'uploaded') {
            // Afficher uniquement les éléments "Uploaded"
            if (statutTexte === 'Uploaded') {
              element.style.display = '';
            } else {
              element.style.display = 'none';
            }
          } else if (status === 'notUploaded') {
            // Afficher uniquement les éléments "Not Uploaded"
            if (statutTexte === 'Not Uploaded') {
              element.style.display = '';
            } else {
              element.style.display = 'none';
            }
          } else {
            // Afficher tous les éléments si aucun statut spécifié
            element.style.display = '';
          }
        });
      }

      // Ajouter les écouteurs d'événements
      boutonUploaded.addEventListener('click', function() {
        console.log('Bouton Uploaded cliqué');
        filtrerParStatutUpload('uploaded');
      });

      boutonNotUploaded.addEventListener('click', function() {
        console.log('Bouton Not Uploaded cliqué');
        filtrerParStatutUpload('notUploaded');
      });

      // Fonction pour réinitialiser (optionnel)
      function afficherTout() {
        const slider = document.querySelector('.slider');
        if (!slider) return;

        const elementsDesign = slider.querySelectorAll('.desimg');
        elementsDesign.forEach(element => {
          element.style.display = '';
        });
      }

      console.log('Filtres initialisés correctement');
    } else {
      console.error('Boutons de filtrage non trouvés');
    }
  });
//***************************************************************************************************************************************/
// Fonction principale pour gérer la sauvegarde des designs sélectionnés
document.getElementById('uploadesigns').addEventListener('click', function() {
    console.log('Bouton "Upload Design" cliqué');

    // Récupérer toutes les checkboxes sélectionnées
    const selectedCheckboxes = document.querySelectorAll('.chqbox[name="uploadSelect"]:checked');
    console.log('Designs sélectionnés:', selectedCheckboxes.length);

    if (selectedCheckboxes.length === 0) {
      alert('Veuillez sélectionner au moins un design à télécharger.');
      return;
    }

    // Préparer les données des designs sélectionnés
    const designs = [];

    selectedCheckboxes.forEach(checkbox => {
      const designId = checkbox.getAttribute('data-design-id');
      const designContainer = checkbox.closest('.desimg');

      // Récupérer les informations du design
      const imgElement = designContainer.querySelector('img.slide');
      const imgSrc = imgElement.getAttribute('src'); // Utiliser getAttribute au lieu de .src
      const title = designContainer.querySelector('h3').textContent;
      const brand = designContainer.querySelector('span').textContent;

      console.log('Traitement du design:', { designId, path: imgSrc, title, brand });

      // Ajouter les informations du design
      designs.push({
        id: designId,
        path: imgSrc,
        title: title,
        brand: brand
      });
    });

    // Afficher un message de chargement
    const loadingMessage = showLoading("Sauvegarde des designs sur le bureau...");

    // Envoyer la requête à Flask
    fetch('/api/save_designs_to_desktop', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ designs: designs })
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }
      return response.json();
    })
    .then(result => {
      // Cacher le message de chargement
      hideLoading(loadingMessage);

      console.log('Résultat:', result);

      if (result.success) {
        console.log(`${result.files.length} design(s) sauvegardés avec succès dans ${result.destination}`);
        alert(result.message);
      } else {
        throw new Error(result.error || "Erreur inconnue");
      }
    })
    .catch(error => {
      // Cacher le message de chargement s'il est toujours affiché
      hideLoading(loadingMessage);

      console.error("Erreur:", error);
      alert("Erreur lors de la sauvegarde des designs sur le bureau. Veuillez réessayer.");
    });
  });
//**************Mécanisme de blocage avec délai d'une minute après saisie de clé correcte************************************************* */
//***************defilement slider********************************************************************************************* */
document.addEventListener('DOMContentLoaded', function() {
    const slider = document.querySelector('.slider');
    const prevBtn = document.querySelector('.slider-prev');
    const nextBtn = document.querySelector('.slider-next');

    let scrollInterval;
    const scrollAmount = 300;
    const scrollSpeed = 100; // Intervalle de temps en millisecondes pour le défilement continu

    // Fonction pour faire défiler vers la gauche
    function scrollLeft() {
        slider.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
    }

    // Fonction pour faire défiler vers la droite
    function scrollRight() {
        slider.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }

    // Gestion de l'appui prolongé pour le bouton précédent
    prevBtn.addEventListener('mousedown', function() {
        scrollLeft(); // Défiler immédiatement au premier clic
        scrollInterval = setInterval(scrollLeft, scrollSpeed); // Continuer à défiler si maintenu
    });

    // Gestion de l'appui prolongé pour le bouton suivant
    nextBtn.addEventListener('mousedown', function() {
        scrollRight(); // Défiler immédiatement au premier clic
        scrollInterval = setInterval(scrollRight, scrollSpeed); // Continuer à défiler si maintenu
    });

    // Arrêter le défilement quand on relâche le bouton
    document.addEventListener('mouseup', function() {
        clearInterval(scrollInterval);
    });

    // Arrêter le défilement si le curseur quitte la zone
    document.addEventListener('mouseleave', function() {
        clearInterval(scrollInterval);
    });

    // Support tactile
    prevBtn.addEventListener('touchstart', function(e) {
        e.preventDefault(); // Empêcher le comportement par défaut
        scrollLeft();
        scrollInterval = setInterval(scrollLeft, scrollSpeed);
    });

    nextBtn.addEventListener('touchstart', function(e) {
        e.preventDefault(); // Empêcher le comportement par défaut
        scrollRight();
        scrollInterval = setInterval(scrollRight, scrollSpeed);
    });

    // Arrêter le défilement au relâchement tactile
    document.addEventListener('touchend', function() {
        clearInterval(scrollInterval);
    });

    // Pour compatibilité avec les clics simples
    prevBtn.addEventListener('click', function(e) {
        e.preventDefault(); // Éviter les clics accidentels
    });

    nextBtn.addEventListener('click', function(e) {
        e.preventDefault(); // Éviter les clics accidentels
    });
});
//************btns listniches defil******************************************************************* */
// Solution pour la navigation horizontale des listniches
document.addEventListener('DOMContentLoaded', function() {
    // Récupérer les éléments nécessaires
    const listniches = document.querySelector('.listniches');
    const prevButton = document.querySelector('.sliderr-prev');
    const nextButton = document.querySelector('.sliderr-next');
    // Vérifier si les éléments existent
    if (!listniches || !prevButton || !nextButton) {
        console.error('Un ou plusieurs éléments nécessaires sont introuvables');
        return;
    }
    console.log('Initialisation de la navigation pour listniches');
    const scrollAmount = 200;

    // Fonction pour faire défiler vers le haut
    function scrollUp() {
        console.log('Défilement vers le haut');
        listniches.scrollBy({
            top: -scrollAmount,
            behavior: 'smooth'
        });
    }
    // Fonction pour faire défiler vers le bas
    function scrollDown() {
        console.log('Défilement vers le bas');
        listniches.scrollBy({
            top: scrollAmount,
            behavior: 'smooth'
        });
    }
    // Ajouter des écouteurs d'événements aux boutons
    prevButton.addEventListener('click', function() {
        console.log('Bouton prev cliqué');
        scrollUp();
    });
    nextButton.addEventListener('click', function() {
        console.log('Bouton next cliqué');
        scrollDown();
    });
    // Vérifier si les boutons devraient être activés/désactivés
    function updateButtonStates() {
        // Désactiver le bouton précédent si on est au début
        const isAtStart = listniches.scrollTop <= 0;
        prevButton.disabled = isAtStart;
        prevButton.classList.toggle('disabled', isAtStart);

        // Désactiver le bouton suivant si on est à la fin
        const maxScrollTop = listniches.scrollHeight - listniches.clientHeight;
        const isAtEnd = Math.abs(listniches.scrollTop - maxScrollTop) < 1;
        nextButton.disabled = isAtEnd;
        nextButton.classList.toggle('disabled', isAtEnd);

        console.log('scrollTop:', listniches.scrollTop);
        console.log('maxScrollTop:', maxScrollTop);
        console.log('isAtStart:', isAtStart);
        console.log('isAtEnd:', isAtEnd);
    }
    // Mettre à jour l'état des boutons au chargement
    updateButtonStates();

    // Mettre à jour l'état des boutons lors du défilement
    listniches.addEventListener('scroll', updateButtonStates);

    // Mettre à jour l'état des boutons lors du redimensionnement de la fenêtre
    window.addEventListener('resize', updateButtonStates);
});
//*********************mockup******************************************************************************* */
/*
// Fonction simplifiée pour afficher le mockup de t-shirt (COMMENTÉ - UTILISE LE HTML STATIQUE MAINTENANT)
function showTshirtMockup(designId) {
    console.log("Affichage du mockup pour le design ID:", designId);

    // Trouver l'image du design
    let designPath = "";
    let designTitle = "Design";

    try {
        // Essayer de trouver l'image directement
        const designImg = document.querySelector(`.slide[data-design-id="${designId}"]`);

        if (designImg) {
            // Image trouvée directement
            const parentContainer = designImg.closest('.desimg');
            designPath = designImg.src;
            designTitle = parentContainer?.querySelector('h3')?.textContent || "Design";
        } else {
            // Essayer de trouver via le bouton
            const mockupButton = document.querySelector(`.mockupbtnn[data-design-id="${designId}"]`);
            if (!mockupButton) {
                alert("Design non trouvé");
                return;
            }

            const parentContainer = mockupButton.closest('.desimg');
            if (!parentContainer) {
                alert("Container du design non trouvé");
                return;
            }

            const imgFromButton = parentContainer.querySelector('.slide');
            if (!imgFromButton) {
                alert("Image non trouvée");
                return;
            }

            designPath = imgFromButton.src;
            designTitle = parentContainer.querySelector('h3')?.textContent || "Design";
        }
    } catch (error) {
        console.error("Erreur:", error);
        alert("Erreur lors de l'affichage du mockup");
        return;
    }

    // Mettre à jour le formulaire statique
    document.getElementById('static-mockup-title').textContent = designTitle;
    document.getElementById('static-tshirt-design').src = designPath;
    document.getElementById('static-tshirt-design').alt = designTitle;

    // Réinitialiser à la couleur noire par défaut
    document.querySelectorAll('.color-option').forEach(opt => opt.classList.remove('active'));
    document.querySelector('.color-option').classList.add('active');
    document.getElementById('static-tshirt-base').src = 'static/tshirt-mockups/Mockup-01.png';
    document.getElementById('static-tshirt-design').style.mixBlendMode = 'screen';
    // Le nom de la couleur n'est plus affiché

    // Afficher la modal
    document.getElementById('static-mockup-modal').style.display = 'flex';
    document.body.style.overflow = 'hidden';
}

// Ajouter les gestionnaires d'événements pour les boutons Mockup (COMMENTÉ - UTILISE LE HTML STATIQUE MAINTENANT)
document.addEventListener('DOMContentLoaded', function() {
    console.log("Initialisation des gestionnaires d'événements pour les boutons mockup");

    // Attacher les gestionnaires directement aux boutons existants
    const mockupButtons = document.querySelectorAll('.mockupbtnn');
    console.log("Nombre de boutons mockup trouvés:", mockupButtons.length);

    mockupButtons.forEach(button => {
        console.log("Configuration du bouton mockup:", button);
        button.addEventListener('click', function() {
            const designId = this.getAttribute('data-design-id');
            console.log("Bouton mockup cliqué, designId:", designId);
            showTshirtMockup(designId);
        });
    });

    // Également ajouter un gestionnaire global pour les boutons qui pourraient être ajoutés dynamiquement
    document.body.addEventListener('click', function(e) {
        if (e.target.classList.contains('mockupbtnn')) {
            const designId = e.target.getAttribute('data-design-id');
            console.log("Bouton mockup cliqué via délégation d'événement, designId:", designId);
            showTshirtMockup(designId);
        }
    });
});
*/

// Nouvelle fonction simplifiée pour afficher le mockup de t-shirt (COMMENTÉ)
/*
function showTshirtMockup(designId) {
    try {
        // Trouver l'image du design
        let designPath = "";
        let designTitle = "Design";

        // Essayer de trouver l'image directement ou via le bouton
        const designImg = document.querySelector(`.slide[data-design-id="${designId}"]`);

        if (designImg) {
            const parentContainer = designImg.closest('.desimg');
            designPath = designImg.src;
            designTitle = parentContainer?.querySelector('h3')?.textContent || "Design";
        } else {
            const mockupButton = document.querySelector(`.mockupbtnn[data-design-id="${designId}"]`);
            if (!mockupButton) return;

            const parentContainer = mockupButton.closest('.desimg');
            if (!parentContainer) return;

            const imgFromButton = parentContainer.querySelector('.slide');
            if (!imgFromButton) return;

            designPath = imgFromButton.src;
            designTitle = parentContainer.querySelector('h3')?.textContent || "Design";
        }

        // Mettre à jour le formulaire statique
        document.getElementById('static-mockup-title').textContent = designTitle;
        document.getElementById('static-tshirt-design').src = designPath;

        // Afficher la modal
        document.getElementById('static-mockup-modal').style.display = 'flex';
        document.body.style.overflow = 'hidden';
    } catch (error) {
        console.error("Erreur lors de l'affichage du mockup:", error);
    }
}

// Ajouter les gestionnaires d'événements pour les boutons Mockup
document.addEventListener('DOMContentLoaded', function() {
    // Attacher les gestionnaires aux boutons
    document.querySelectorAll('.mockupbtnn').forEach(button => {
        button.addEventListener('click', function() {
            showTshirtMockup(this.getAttribute('data-design-id'));
        });
    });

    // Gestionnaire global pour les boutons ajoutés dynamiquement
    document.body.addEventListener('click', function(e) {
        if (e.target.classList.contains('mockupbtnn')) {
            showTshirtMockup(e.target.getAttribute('data-design-id'));
        }
    });
});
*/

// Fonction pour afficher le mockup de t-shirt - Utilise le formulaire HTML statique
function showTshirtMockup(designId) {
    console.log("Affichage du mockup pour le design ID:", designId);

    try {
        // Trouver l'image du design
        let designPath = "";
        let designTitle = "Design";

        // Essayer de trouver l'image directement ou via le bouton
        const designImg = document.querySelector(`.slide[data-design-id="${designId}"]`);

        if (designImg) {
            // Image trouvée directement
            const parentContainer = designImg.closest('.desimg');
            designPath = designImg.src;
            designTitle = parentContainer?.querySelector('h3')?.textContent || "Design";
        } else {
            // Essayer de trouver via le bouton
            const mockupButton = document.querySelector(`.mockupbtnn[data-design-id="${designId}"]`);
            if (!mockupButton) {
                console.error("Bouton mockup non trouvé pour designId:", designId);
                return;
            }

            const parentContainer = mockupButton.closest('.desimg');
            if (!parentContainer) {
                console.error("Container du design non trouvé");
                return;
            }

            const imgFromButton = parentContainer.querySelector('.slide');
            if (!imgFromButton) {
                console.error("Image du design non trouvée");
                return;
            }

            designPath = imgFromButton.src;
            designTitle = parentContainer.querySelector('h3')?.textContent || "Design";
        }

        console.log("Affichage du formulaire HTML statique");
        console.log("Design Path:", designPath);
        console.log("Design Title:", designTitle);

        // Mettre à jour le formulaire statique
        document.getElementById('static-mockup-title').textContent = designTitle;
        document.getElementById('static-tshirt-design').src = designPath;

        // Réinitialiser à la couleur noire par défaut
        const colorOptions = document.querySelectorAll('.color-grid .color-option');
        if (colorOptions.length > 0) {
            // Simuler un clic sur la première couleur (noir)
            document.getElementById('static-tshirt-container').style.backgroundImage = "url('static/tshirt-mockups/Mockup-01.png')";
            // Le nom de la couleur n'est plus affiché

            // Réinitialiser le wrapper du design pour le t-shirt noir
            const designWrapper = document.getElementById('static-design-wrapper');
            designWrapper.style.backgroundColor = 'transparent';
            designWrapper.style.borderRadius = '0';
            designWrapper.style.padding = '0';
        }

        // Afficher la modal
        const modal = document.getElementById('static-mockup-modal');
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden'; // Empêcher le défilement

        console.log("Mockup affiché avec succès pour:", designTitle);
    } catch (error) {
        console.error("Erreur lors de l'affichage du mockup:", error);
        alert("Erreur JavaScript: " + error.message);
    }
}

// Initialiser les gestionnaires d'événements pour les boutons mockup
document.addEventListener('DOMContentLoaded', function() {
    console.log("Initialisation des gestionnaires d'événements pour les boutons mockup");

    // Attacher les gestionnaires aux boutons existants
    document.querySelectorAll('.mockupbtnn').forEach(button => {
        button.addEventListener('click', function() {
            const designId = this.getAttribute('data-design-id');
            console.log("Bouton mockup cliqué, designId:", designId);
            showTshirtMockup(designId);
        });
    });

    // Gestionnaire global pour les boutons qui pourraient être ajoutés dynamiquement
    document.body.addEventListener('click', function(e) {
        if (e.target.classList.contains('mockupbtnn')) {
            const designId = e.target.getAttribute('data-design-id');
            console.log("Bouton mockup cliqué via délégation d'événement, designId:", designId);
            showTshirtMockup(designId);
        }
    });

    // Gestionnaires pour fermer la modal
    const closeButton = document.getElementById('static-mockup-close');
    const closeButtonBottom = document.querySelector('.demockup-button');
    const modal = document.getElementById('static-mockup-modal');

    if (closeButton) {
        closeButton.addEventListener('click', function() {
            modal.style.display = 'none';
            document.body.style.overflow = '';
        });
    }

    if (closeButtonBottom) {
        closeButtonBottom.addEventListener('click', function() {
            modal.style.display = 'none';
            document.body.style.overflow = '';
        });
    }

    // Fermer en cliquant à l'extérieur
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.style.display = 'none';
            document.body.style.overflow = '';
        }
    });

    // Ajouter des gestionnaires pour les miniatures de t-shirts (div avec background-image)
    document.querySelectorAll('.color-grid .color-option').forEach((colorDiv, index) => {
        colorDiv.addEventListener('click', function() {
            // Numéro du mockup (de 1 à 30)
            const mockupNumber = index + 1;
            const mockupFile = `Mockup-${mockupNumber.toString().padStart(2, '0')}.png`;

            // Mettre à jour le background-image du conteneur de t-shirt
            document.getElementById('static-tshirt-container').style.backgroundImage = `url('static/tshirt-mockups/${mockupFile}')`;

            // Traiter toutes les couleurs de la même façon, y compris le blanc
            document.getElementById('static-tshirt-design').style.display = 'block';

            // Le nom de la couleur n'est plus affiché
        });
    });

    // Compatibilité avec l'ancien code (pour les img)
    document.querySelectorAll('.color-grid img').forEach((img, index) => {
        img.addEventListener('click', function() {
            // Numéro du mockup (de 1 à 30)
            const mockupNumber = index + 1;
            const mockupFile = `Mockup-${mockupNumber.toString().padStart(2, '0')}.png`;

            // Mettre à jour le background-image du conteneur de t-shirt
            document.getElementById('static-tshirt-container').style.backgroundImage = `url('static/tshirt-mockups/${mockupFile}')`;

            // Traiter toutes les couleurs de la même façon, y compris le blanc
            document.getElementById('static-tshirt-design').style.display = 'block';

            // Le nom de la couleur n'est plus affiché
        });
    });
});
//************************************************************************************************************** */
// Fonction pour gérer les boutons de toggle (Uploaded/Not Uploaded)
function setupToggleButtons() {
    const uploadedBtn = document.getElementById('upld');
    const notUploadedBtn = document.getElementById('notup');

    if (!uploadedBtn || !notUploadedBtn) {
        console.error('Boutons de toggle non trouvés');
        return;
    }

    // Fonction pour activer un bouton et désactiver l'autre
    function toggleActive(activeBtn, inactiveBtn) {
        activeBtn.classList.add('active');
        inactiveBtn.classList.remove('active');
    }

    // Fonction pour filtrer les designs
    function filterDesigns(showUploaded) {
        const designs = document.querySelectorAll('.desimg');

        designs.forEach(design => {
            const uploadStatus = design.querySelector('.uplod');

            if (!uploadStatus) return;

            const isUploaded = uploadStatus.textContent.trim() === 'Uploaded';

            // Si showUploaded est true, on affiche les designs uploadés
            // Si showUploaded est false, on affiche les designs non uploadés
            if (showUploaded === isUploaded) {
                design.style.display = 'flex'; // Afficher le design
                design.style.opacity = '1';
            } else {
                // Au lieu de cacher complètement, on peut réduire l'opacité et désactiver les interactions
                design.style.opacity = '0.3';
                design.style.pointerEvents = 'none';
            }
        });
    }

    // Écouteurs d'événements pour les boutons
    uploadedBtn.addEventListener('click', function() {
        toggleActive(uploadedBtn, notUploadedBtn);
        filterDesigns(true); // Afficher les designs uploadés
        console.log('Affichage des designs uploadés');
    });

    notUploadedBtn.addEventListener('click', function() {
        toggleActive(notUploadedBtn, uploadedBtn);
        filterDesigns(false); // Afficher les designs non uploadés
        console.log('Affichage des designs non uploadés');
    });

    // Filtrer par défaut selon le bouton actif au chargement
    if (uploadedBtn.classList.contains('active')) {
        filterDesigns(true);
    } else if (notUploadedBtn.classList.contains('active')) {
        filterDesigns(false);
    }
}

/**
 * Fonction pour rendre les formulaires déplaçables
 * Permet à l'utilisateur de déplacer les formulaires en cliquant et en faisant glisser leur en-tête
 */
function setupDraggableForms() {
    // Sélectionner tous les formulaires déplaçables
    const draggableForms = document.querySelectorAll('.draggable-form');

    draggableForms.forEach(form => {
        const header = form.querySelector('.draggable-header');
        const closeBtn = form.querySelector('.close-btn');

        if (!header) return;

        let isDragging = false;
        let hasMoved = false;
        let offsetX, offsetY;
        let startX, startY;

        // Fonction pour commencer le déplacement
        function startDrag(e) {
            // Vérifier si c'est un clic gauche (pour la souris) ou un toucher (pour mobile)
            if ((e.type === 'mousedown' && e.button !== 0) ||
                (e.type === 'touchstart' && e.touches.length !== 1)) {
                return;
            }

            // Empêcher le comportement par défaut
            e.preventDefault();

            // Obtenir la position initiale
            const clientX = e.type === 'touchstart' ? e.touches[0].clientX : e.clientX;
            const clientY = e.type === 'touchstart' ? e.touches[0].clientY : e.clientY;

            // Enregistrer la position de départ pour détecter si l'utilisateur a vraiment déplacé le formulaire
            startX = clientX;
            startY = clientY;
            hasMoved = false;

            // Calculer le décalage entre le point de clic et le coin supérieur gauche du formulaire
            const rect = form.getBoundingClientRect();
            offsetX = clientX - rect.left;
            offsetY = clientY - rect.top;

            // Activer le déplacement
            isDragging = true;

            // Ajouter une classe pour indiquer que le formulaire est en cours de déplacement
            form.classList.add('dragging');

            // Supprimer la transformation pour permettre un positionnement absolu
            form.style.transform = 'none';
            form.style.top = rect.top + 'px';
            form.style.left = rect.left + 'px';
        }

        // Fonction pour effectuer le déplacement
        function drag(e) {
            if (!isDragging) return;

            // Empêcher le comportement par défaut
            e.preventDefault();

            // Obtenir la position actuelle
            const clientX = e.type === 'touchmove' ? e.touches[0].clientX : e.clientX;
            const clientY = e.type === 'touchmove' ? e.touches[0].clientY : e.clientY;

            // Vérifier si l'utilisateur a vraiment déplacé le formulaire (plus de 5px)
            if (!hasMoved) {
                const deltaX = Math.abs(clientX - startX);
                const deltaY = Math.abs(clientY - startY);
                if (deltaX > 5 || deltaY > 5) {
                    hasMoved = true;
                }
            }

            // Calculer la nouvelle position
            const newLeft = clientX - offsetX;
            const newTop = clientY - offsetY;

            // Appliquer la nouvelle position
            form.style.left = newLeft + 'px';
            form.style.top = newTop + 'px';
        }

        // Fonction pour terminer le déplacement
        function stopDrag(e) {
            if (!isDragging) return;

            // Désactiver le déplacement
            isDragging = false;

            // Supprimer la classe de déplacement
            form.classList.remove('dragging');

            // Si l'utilisateur a cliqué sur le bouton de fermeture, ne pas empêcher l'événement
            if (hasMoved) {
                // L'utilisateur a déplacé le formulaire, empêcher le clic sur le bouton de fermeture
                e.preventDefault();
                e.stopPropagation();
            }
        }

        // Empêcher les clics sur le bouton de fermeture pendant le déplacement
        if (closeBtn) {
            closeBtn.addEventListener('click', function(e) {
                if (isDragging || hasMoved) {
                    e.preventDefault();
                    e.stopPropagation();
                    hasMoved = false;
                }
            });
        }

        // Ajouter les écouteurs d'événements pour la souris
        header.addEventListener('mousedown', startDrag);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', stopDrag);

        // Ajouter les écouteurs d'événements pour le tactile
        header.addEventListener('touchstart', startDrag);
        document.addEventListener('touchmove', drag);
        document.addEventListener('touchend', stopDrag);
    });
}

/**
 * Fonction pour copier le contenu d'un élément dans le presse-papier
 * lorsque l'utilisateur double-clique dessus
 */
function setupClipboardFunctionality() {
    // Fonction pour copier le texte dans le presse-papier
    function copyToClipboard(text) {
        // Créer un élément textarea temporaire
        const textarea = document.createElement('textarea');
        textarea.value = text;

        // Rendre l'élément invisible
        textarea.style.position = 'fixed';
        textarea.style.opacity = 0;

        // Ajouter l'élément au DOM
        document.body.appendChild(textarea);

        // Sélectionner le texte
        textarea.select();
        textarea.setSelectionRange(0, 99999); // Pour les appareils mobiles

        // Copier le texte
        try {
            const successful = document.execCommand('copy');
            const msg = successful ? 'copié' : 'échec';
            console.log(`Texte ${msg} dans le presse-papier: ${text}`);

            // Afficher une notification à l'utilisateur
            showNotification(`"${text}" a été copié dans le presse-papier`);
        } catch (err) {
            console.error('Erreur lors de la copie:', err);
        }

        // Supprimer l'élément temporaire
        document.body.removeChild(textarea);
    }

    // Fonction pour afficher une notification
    function showNotification(message) {
        // Créer l'élément de notification
        const notification = document.createElement('div');
        notification.className = 'clipboard-notification';
        notification.textContent = message;

        // Ajouter la notification au DOM
        document.body.appendChild(notification);

        // Afficher la notification avec une animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // Supprimer la notification après 2 secondes
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 2000);
    }

    // Ajouter des écouteurs d'événements pour les titres (h3) et les marques (span) des designs
    function setupClipboardListeners() {
        // Sélectionner tous les titres et marques des designs
        const designTitles = document.querySelectorAll('.desimg h3');
        const designBrands = document.querySelectorAll('.desimg span');

        // Ajouter des écouteurs d'événements pour les titres
        designTitles.forEach(title => {
            title.addEventListener('dblclick', function(e) {
                e.preventDefault();
                copyToClipboard(this.textContent.trim());
            });

            // Ajouter un curseur et une info-bulle pour indiquer que l'élément est copiable
            title.style.cursor = 'copy';
            title.title = 'Double-cliquez pour copier le titre';
        });

        // Ajouter des écouteurs d'événements pour les marques
        designBrands.forEach(brand => {
            brand.addEventListener('dblclick', function(e) {
                e.preventDefault();
                copyToClipboard(this.textContent.trim());
            });

            // Ajouter un curseur et une info-bulle pour indiquer que l'élément est copiable
            brand.style.cursor = 'copy';
            brand.title = 'Double-cliquez pour copier la marque';
        });
    }

    // Initialiser les écouteurs d'événements
    setupClipboardListeners();

    // Observer les changements dans le DOM pour ajouter des écouteurs aux nouveaux éléments
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length) {
                setupClipboardListeners();
            }
        });
    });

    // Observer les changements dans le conteneur des designs
    const slider = document.querySelector('.slider');
    if (slider) {
        observer.observe(slider, { childList: true, subtree: true });
    }
}

function initializeApplication() {
    // Initialize modules when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        const sliderElement = document.querySelector('.slider');
        if (sliderElement) sliderElement.innerHTML = '';
        DateTimeManager.init();
        SimpleSlider.init();
        NicheManager.initCategoryButtons();
        NicheFormManager.init();
        setupToggleButtons(); // Initialiser les boutons de toggle
        setupDraggableForms(); // Initialiser les formulaires déplaçables
        setupClipboardFunctionality(); // Initialiser la fonctionnalité de copie dans le presse-papier
    });
    // Initialize modules that need window.onload
    window.onload = function() {
        ImageZoom.init();
    };
}
// Lancer l'initialisation de l'application
initializeApplication();