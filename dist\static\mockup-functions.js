// Fonction pour afficher les mockups de t-shirts
function showTshirtMockup(designId) {
    try {
        // Trouver l'image du design
        let designPath = "";
        let designTitle = "Design";

        // Essayer de trouver l'image directement
        const designImg = document.querySelector(`.slide[data-design-id="${designId}"]`);

        if (designImg) {
            // Image trouvée directement
            const parentContainer = designImg.closest('.desimg');
            designPath = designImg.src;
            designTitle = parentContainer?.querySelector('h3')?.textContent || "Design";
        } else {
            // Essayer de trouver via le bouton
            const mockupButton = document.querySelector(`.mockupbtnn[data-design-id="${designId}"]`);
            if (!mockupButton) {
                console.error("Bouton mockup non trouvé pour designId:", designId);
                return;
            }

            const parentContainer = mockupButton.closest('.desimg');
            if (!parentContainer) {
                console.error("Container du design non trouvé");
                return;
            }

            const imgFromButton = parentContainer.querySelector('.slide');
            if (!imgFromButton) {
                console.error("Image du design non trouvée");
                return;
            }

            designPath = imgFromButton.src;
            designTitle = parentContainer.querySelector('h3')?.textContent || "Design";
        }

        // Afficher le mockup en utilisant la version HTML statique
        showStaticMockup(designPath, designTitle);
    } catch (error) {
        console.error("Erreur générale:", error);
        alert("Erreur lors de l'affichage du mockup: " + error.message);
    }
}

// Fonction pour afficher le mockup de t-shirt
function showStaticMockup(designPath, designTitle) {
    try {
        // Mettre à jour le titre du mockup
        document.getElementById('static-mockup-title').textContent = designTitle;

        // S'assurer que le chemin de l'image est absolu
        if (!designPath.startsWith('http') && !designPath.startsWith('/')) {
            designPath = '/' + designPath;
        }

        // Récupérer les éléments du DOM
        const designImage = document.getElementById('static-tshirt-design');
        const designWrapper = document.getElementById('static-design-wrapper');
        const tshirtContainer = document.getElementById('static-tshirt-container');

        // Configurer l'image du design
        designImage.src = designPath;
        designImage.alt = designTitle;

        // S'assurer que l'image est visible
        designImage.style.display = 'block';

        // Récupérer les paramètres de configuration
        const config = MOCKUP_CONFIG.mockupSettings || {
            mockupPath: 'static/tshirt-mockups/',
            designMaxWidth: 100,
            designMaxHeight: 100
        };

        // Forcer les dimensions du design à 100% de son conteneur
        designImage.style.maxWidth = "100%";
        designImage.style.maxHeight = "100%";
        designImage.style.width = "100%";
        designImage.style.height = "auto";

        // S'assurer que le design est centré horizontalement
        designWrapper.style.left = "50%";
        designWrapper.style.transform = "translate(-50%, -50%)";

        // Réinitialiser à la couleur noire par défaut (Mockup-01.png)
        const defaultMockupPath = `${config.mockupPath || 'static/tshirt-mockups/'}Mockup-01.png`;
        tshirtContainer.style.backgroundImage = `url('${defaultMockupPath}')`;

        // Le nom de la couleur n'est plus affiché

        // Réinitialiser le design wrapper pour le t-shirt noir
        designWrapper.style.backgroundColor = 'transparent';
        designWrapper.style.borderRadius = '0';
        designWrapper.style.padding = '0';

        // Afficher la modal
        const modal = document.getElementById('static-mockup-modal');
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden'; // Empêcher le défilement

    } catch (error) {
        console.error("Erreur lors de l'affichage du mockup:", error);
        alert("Erreur lors de l'affichage du mockup: " + error.message);
    }
}

// Gestionnaires d'événements pour les boutons mockup
document.addEventListener('DOMContentLoaded', function() {
    // Configuration des boutons mockup (existants et futurs via délégation)
    document.body.addEventListener('click', function(e) {
        // Trouver le bouton mockup le plus proche (peut être l'élément lui-même ou un enfant)
        const mockupButton = e.target.closest('.mockupbtnn');
        if (mockupButton) {
            e.preventDefault();
            const designId = mockupButton.getAttribute('data-design-id');
            showTshirtMockup(designId);
        }
    });

    // Gestionnaires pour fermer la modal
    const closeButton = document.getElementById('static-mockup-close');
    const closeButtonBottom = document.querySelector('.demockup-button');
    const modal = document.getElementById('static-mockup-modal');

    if (closeButton) {
        closeButton.addEventListener('click', function() {
            modal.style.display = 'none';
            document.body.style.overflow = '';
        });
    }

    if (closeButtonBottom) {
        closeButtonBottom.addEventListener('click', function() {
            modal.style.display = 'none';
            document.body.style.overflow = '';
        });
    }

    // Fermer en cliquant à l'extérieur
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.style.display = 'none';
            document.body.style.overflow = '';
        }
    });

    // Configuration des options de couleur
    setupColorOptions();
});

// Configuration des options de couleur pour les t-shirts
function setupColorOptions() {
    // Récupérer la configuration
    const config = MOCKUP_CONFIG || {
        mockupSettings: {
            mockupPath: 'static/tshirt-mockups/'
        },
        colorNames: [
            'Noir', 'Anthracite', 'Bleu', 'Marron Foncé', 'Rouge Vif', 'Gris Foncé',
            'Gris', 'Gris Foncé', 'Vert', 'Vert Foncé', 'Bleu Marine', 'Anthracite',
            'Olive', 'Gris', 'Bleu', 'Gris', 'Rose Vif', 'Gris', 'Bordeaux', 'Violet',
            'Orange', 'Bordeaux', 'Vert Foncé', 'Blanc', 'Bleu Marine', 'Rouge',
            'Violet', 'Rose', 'Rouge', 'Vert Foncé'
        ]
    };

    // Extraire les paramètres de configuration
    const colorNames = config.colorNames || [];
    const mockupBasePath = config.mockupSettings?.mockupPath || 'static/tshirt-mockups/';

    // Sélectionner toutes les options de couleur
    const colorOptions = document.querySelectorAll('.color-grid .color-option');

    colorOptions.forEach((colorOption, index) => {
        // Ajouter un gestionnaire d'événement mouseover (survol de souris)
        colorOption.addEventListener('mouseover', function() {
            // Numéro du mockup (de 1 à 30)
            const mockupNumber = index + 1;
            const mockupFile = `Mockup-${mockupNumber.toString().padStart(2, '0')}.png`;
            const mockupPath = `${mockupBasePath}${mockupFile}`;

            // Récupérer le nom de la couleur (avec fallback)
            const colorName = colorNames[index] || `Couleur ${mockupNumber}`;

            // Récupérer les éléments du DOM
            const tshirtContainer = document.getElementById('static-tshirt-container');
            const designImage = document.getElementById('static-tshirt-design');
            const designWrapper = document.getElementById('static-design-wrapper');

            // Mettre à jour le conteneur du t-shirt
            tshirtContainer.style.backgroundImage = `url('${mockupPath}')`;

            // Traiter toutes les couleurs de la même façon
            designWrapper.style.backgroundColor = 'transparent';
            designWrapper.style.borderRadius = '0';
            designWrapper.style.padding = '0';

            // S'assurer que l'image du design est toujours visible et à la bonne taille
            designImage.style.display = 'block';
            designImage.style.maxWidth = "100%";
            designImage.style.maxHeight = "100%";
            designImage.style.width = "100%";
            designImage.style.height = "auto";

            // S'assurer que le design est centré horizontalement
            designWrapper.style.left = "50%";
            designWrapper.style.transform = "translate(-50%, -50%)";
        });
    });
}
