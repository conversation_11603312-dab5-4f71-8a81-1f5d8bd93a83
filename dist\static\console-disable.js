/**
 * Script pour désactiver les instructions console.log() en production
 * Cela améliore les performances en évitant les opérations d'I/O inutiles
 */

// Fonction exécutée immédiatement
(function() {
    // Vérifier si nous sommes en production (vous pouvez ajuster cette condition)
    const isProduction = true; // Mettre à true pour désactiver les logs

    // Si nous sommes en production, remplacer les méthodes de console par des fonctions vides
    if (isProduction) {
        // Sauvegarder les méthodes originales (pour le débogage si nécessaire)
        const originalConsole = {
            log: console.log,
            info: console.info,
            warn: console.warn,
            debug: console.debug
        };

        // Remplacer les méthodes par des fonctions vides
        console.log = function() {};
        console.info = function() {};
        console.warn = function() {};
        console.debug = function() {};

        // Conserver console.error pour les erreurs importantes
        // console.error reste inchangé pour capturer les erreurs critiques
    }
})();
